import 'dart:io';

import 'package:astreal/models/chart_settings.dart';
import 'package:astreal/services/ai_interpretation_service.dart';
import 'package:astreal/services/chart_service.dart';
import 'package:astreal/services/firebase_service.dart';
import 'package:astreal/services/location_service.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:astreal/ui/pages/pages.dart';
import 'package:astreal/ui/theme_provider.dart';
import 'package:astreal/utils/LoggerUtils.dart';
import 'package:astreal/utils/geocoding_service.dart';
import 'package:astreal/viewmodels/files_viewmodel.dart';
import 'package:astreal/viewmodels/recent_charts_viewmodel.dart';
import 'package:astreal/viewmodels/settings_viewmodel.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'firebase_options.dart';
import 'web_helper.dart' if (dart.library.ffi) 'io_helper.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  // 確保 Flutter 綁定在應用啟動前初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 設定 HTTP 覆寫
  HttpOverrides.global = MyHttpOverrides();

  // 設定導航欄與狀態列樣式
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent, // 狀態列透明
    statusBarIconBrightness: Brightness.dark, // 狀態列圖示深色（白底）
    systemNavigationBarColor: Colors.white, // 導航欄白色
    systemNavigationBarIconBrightness: Brightness.dark, // 導航欄圖示深色
  ));

  // 初始化 Firebase
  try {
    // 使用 DefaultFirebaseOptions 初始化 Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // 綜合使用 FirebaseService 初始化
    await FirebaseService.initialize();
    logger.i('Firebase 初始化成功');
  } catch (e) {
    logger.e('Firebase 初始化失敗: $e');
    // 繼續執行應用程式，即使 Firebase 初始化失敗
  }

  // 初始化 Swiss Ephemeris
  // try {
  //   await initSweph();
  // } catch (e) {
  //   print('Swiss Ephemeris 初始化失敗: $e');
  //   // 繼續執行應用程式，即使 Swiss Ephemeris 初始化失敗
  // }
  await initSweph([
    'packages/sweph/assets/ephe/seas_18.se1', // For house calc
    'packages/sweph/assets/ephe/sefstars.txt', // For star position
    'packages/sweph/assets/ephe/seasnam.txt', // For asteriods
  ]);
  runApp(const AstrealApp());
}

/// 檢查星曆文件是否已存在，如果不存在則從資源目錄複製
Future<void> _copyEpheFilesIfNeeded(String targetDir) async {
  final epheFiles = [
    'seas_18.se1',
    'sefstars.txt',
    'seasnam.txt',
    'sepl_18.se1',
    'semo_18.se1',
    'se00433s.se1',
  ];

  // 先清空目標目錄，確保沒有舊文件
  try {
    final dir = Directory(targetDir);
    if (await dir.exists()) {
      await dir.delete(recursive: true);
      await dir.create(recursive: true);
      print('已重新創建目錄: $targetDir');
    }
  } catch (e) {
    print('重新創建目錄失敗: $e');
  }

  for (final fileName in epheFiles) {
    final targetFile = File('$targetDir/$fileName');
    try {
      // 從 Flutter 資源中讀取文件
      final ByteData data = await rootBundle.load('assets/ephe/$fileName');
      final List<int> bytes = data.buffer.asUint8List();

      // 寫入目標文件
      await targetFile.writeAsBytes(bytes);
      print('成功複製星曆文件: $fileName');

      // 確認文件存在且可讀
      if (await targetFile.exists()) {
        final fileSize = await targetFile.length();
        print('文件 $fileName 已存在，大小: $fileSize 字節');
      } else {
        print('警告: 文件 $fileName 寫入後仍無法找到');
      }
    } catch (e) {
      print('複製星曆文件失敗: $fileName, 錯誤: $e');
    }
  }

  // 列出目錄中的所有文件
  try {
    final dir = Directory(targetDir);
    final List<FileSystemEntity> entities = await dir.list().toList();
    print('目錄 $targetDir 中的文件:');
    for (var entity in entities) {
      if (entity is File) {
        final size = await entity.length();
        print('  - ${entity.path.split('/').last} (大小: $size 字節)');
      }
    }
  } catch (e) {
    print('列出目錄內容失敗: $e');
  }
}

class AstrealApp extends StatelessWidget {
  const AstrealApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Using MultiProvider for app-wide providers
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => SettingsViewModel()),
        ChangeNotifierProvider(create: (_) => FilesViewModel()),
        ChangeNotifierProvider(create: (_) => RecentChartsViewModel()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          return MaterialApp(
            title: 'Astreal',
            theme: AstrealAppTheme.lightTheme,
            darkTheme: AstrealAppTheme.lightTheme,
            themeMode: themeProvider.themeMode,
            home: MultiProvider(
              providers: [
                Provider<ChartService>(create: (_) => ChartService()),
                Provider<LocationService>(create: (_) => LocationService()),
                Provider<GeocodingService>(create: (_) => GeocodingService()),
                Provider<AIInterpretationService>(
                    create: (_) => AIInterpretationService()),
                FutureProvider<ChartSettings>(
                  create: (_) => ChartSettings.loadFromPrefs(),
                  initialData: ChartSettings(),
                ),
              ],
              child: const MainScreen(),
            ),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  // 頁面列表
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();

    // 判斷是否為開發模式
    const bool isInDevelopmentMode = kDebugMode;

    // 根據開發模式決定頁面列表
    if (isInDevelopmentMode) {
      _pages = [
        const HomePage(),
        const FilesPage(),
        const AnalysisPage(), // 開發模式顯示分析頁
        const BookingListPage(), // 開發模式顯示預約列表頁
        const SettingsPage(), // 開發模式顯示設定頁
      ];
    } else {
      _pages = [
        const HomePage(),
        const FilesPage(),
        // 非開發模式不顯示分析頁和設定頁
        const SettingsPage(),
      ];
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 確保選擇的索引在有效範圍內
    final int safeIndex = _selectedIndex < _pages.length ? _selectedIndex : 0;

    return Scaffold(
      body: _pages[safeIndex],
      bottomNavigationBar: NavigationBar(
        destinations: const [
          NavigationDestination(
            icon: Icon(Icons.home_outlined),
            selectedIcon: Icon(Icons.home),
            label: '首頁',
          ),
          NavigationDestination(
            icon: Icon(Icons.folder_outlined),
            selectedIcon: Icon(Icons.folder),
            label: '檔案',
          ),
          // 只在開發模式下顯示分析頁
          if (kDebugMode)
            NavigationDestination(
              icon: Icon(Icons.analytics_outlined),
              selectedIcon: Icon(Icons.analytics),
              label: '分析',
            ),
          // 只在開發模式下顯示預約頁
          if (kDebugMode)
            NavigationDestination(
              icon: Icon(Icons.event_outlined),
              selectedIcon: Icon(Icons.event),
              label: '預約',
            ),
          NavigationDestination(
            icon: Icon(Icons.settings_outlined),
            selectedIcon: Icon(Icons.settings),
            label: '設定',
          ),
        ],
        selectedIndex: safeIndex,
        onDestinationSelected: _onItemTapped,
        elevation: 0,
        height: 65,
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
      ),
    );
  }
}
