

/// Represents an AI model that can be used for chart interpretation
class AIModel {
  final String name;
  final String id;
  final String description;

  const AIModel({
    required this.name,
    required this.id,
    required this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
