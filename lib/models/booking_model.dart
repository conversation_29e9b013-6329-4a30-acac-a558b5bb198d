import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 占星諮詢預約模型
class BookingModel {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String contactMethod; // 新增：聯絡方式
  final DateTime bookingDate;
  final TimeOfDay bookingTime;
  final String consultationType;
  final String? notes;
  final bool isConfirmed;
  final DateTime createdAt;
  final Map<String, dynamic>? birthData; // 星盤資料
  final String? deviceId; // 裝置唯一識別碼

  BookingModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.contactMethod,
    required this.bookingDate,
    required this.bookingTime,
    required this.consultationType,
    this.notes,
    this.isConfirmed = false,
    DateTime? createdAt,
    this.birthData,
    this.deviceId,
  }) : createdAt = createdAt ?? DateTime.now();

  // 從 JSON 創建 BookingModel
  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      contactMethod: json['contactMethod'] as String,
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      bookingTime: TimeOfDay(
        hour: json['bookingTimeHour'] as int,
        minute: json['bookingTimeMinute'] as int,
      ),
      consultationType: json['consultationType'] as String,
      notes: json['notes'] as String?,
      isConfirmed: json['isConfirmed'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      birthData: json['birthData'] != null ? Map<String, dynamic>.from(json['birthData']) : null,
      deviceId: json['deviceId'] as String?,
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'contactMethod': contactMethod,
      'bookingDate': DateFormat('yyyy-MM-dd HH:mm').format(bookingDate),
      'bookingTimeHour': bookingTime.hour,
      'bookingTimeMinute': bookingTime.minute,
      'consultationType': consultationType,
      'notes': notes,
      'isConfirmed': isConfirmed,
      'createdAt': DateFormat('yyyy-MM-dd HH:mm').format(createdAt),
      'birthData': birthData,
      'deviceId': deviceId,
    };
  }

  // 創建更新後的預約
  BookingModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? contactMethod,
    DateTime? bookingDate,
    TimeOfDay? bookingTime,
    String? consultationType,
    String? notes,
    bool? isConfirmed,
    DateTime? createdAt,
    Map<String, dynamic>? birthData,
    String? deviceId,
  }) {
    return BookingModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      contactMethod: contactMethod ?? this.contactMethod,
      bookingDate: bookingDate ?? this.bookingDate,
      bookingTime: bookingTime ?? this.bookingTime,
      consultationType: consultationType ?? this.consultationType,
      notes: notes ?? this.notes,
      isConfirmed: isConfirmed ?? this.isConfirmed,
      createdAt: createdAt ?? this.createdAt,
      birthData: birthData ?? this.birthData,
      deviceId: deviceId ?? this.deviceId,
    );
  }
}

/// 諮詢類型
class ConsultationType {
  static const String natal = '本命盤解析';
  static const String transit = '行運盤解析';
  // static const String relationship = '關係合盤解析';
  static const String career = '職業發展諮詢';
  // static const String lifeDirection = '人生方向諮詢';
  // static const String timing = '擇時諮詢';

  static List<String> get all => [
        natal,
        transit,
        // relationship,
        career,
        // lifeDirection,
        // timing,
      ];
}
