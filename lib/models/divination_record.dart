import 'package:cloud_firestore/cloud_firestore.dart';

/// 卜卦記錄模型
class DivinationRecord {
  final String id;
  final String question;
  final String location;
  final DateTime timestamp;
  final String type; // 'horary', 'yijing', 'tarot'
  final String result;
  final Map<String, dynamic>? chartData;

  DivinationRecord({
    required this.id,
    required this.question,
    required this.location,
    required this.timestamp,
    required this.type,
    required this.result,
    this.chartData,
  });

  /// 從 JSON 創建卜卦記錄
  factory DivinationRecord.fromJson(Map<String, dynamic> json) {
    return DivinationRecord(
      id: json['id'] as String,
      question: json['question'] as String,
      location: json['location'] as String,
      timestamp: json['timestamp'] is String
          ? DateTime.parse(json['timestamp'] as String)
          : (json['timestamp'] as Timestamp).toDate(),
      type: json['type'] as String,
      result: json['result'] as String,
      chartData: json['chartData'] as Map<String, dynamic>?,
    );
  }

  /// 轉換為 JSON（用於本地存儲）
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'location': location,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'result': result,
      'chartData': chartData,
    };
  }

  /// 轉換為 Firestore JSON（如果需要上傳到 Firestore）
  Map<String, dynamic> toFirestoreJson() {
    return {
      'id': id,
      'question': question,
      'location': location,
      'timestamp': Timestamp.fromDate(timestamp),
      'type': type,
      'result': result,
      'chartData': chartData,
    };
  }
}
