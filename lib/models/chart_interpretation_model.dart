

class ChartInterpretationModel {
  final String chartType;
  final PersonInfo primaryPerson;
  final List<PlanetInfo> planets;
  final List<ChartAspectInfo> aspects;
  final Map<String, double> houses;

  ChartInterpretationModel({
    required this.chartType,
    required this.primaryPerson,
    required this.planets,
    required this.aspects,
    required this.houses,
  });

  Map<String, dynamic> toJson() => {
        'chart_type': chartType,
        'primary_person': primaryPerson.toJson(),
        'planets': planets.map((p) => p.toJson()).toList(),
        'aspects': aspects.map((a) => a.toJson()).toList(),
        'houses': houses,
      };
}

class PersonInfo {
  final String name;
  final DateTime birthDate;
  final String birthPlace;

  PersonInfo({
    required this.name,
    required this.birthDate,
    required this.birthPlace,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'birth_date': birthDate.toIso8601String(),
        'birth_place': birthPlace,
      };
}

class PlanetInfo {
  final String name;
  final double longitude;
  final double latitude;
  final double speed;
  final int house;
  final String sign;
  final String symbol;

  PlanetInfo({
    required this.name,
    required this.longitude,
    required this.latitude,
    required this.speed,
    required this.house,
    required this.sign,
    required this.symbol,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'longitude': longitude,
        'latitude': latitude,
        'speed': speed,
        'house': house,
        'sign': sign,
        'symbol': symbol,
      };
}

class ChartAspectInfo {
  final String planet1;
  final String planet2;
  final String aspect;
  final double orb;
  final String symbol;
  final double angle;

  ChartAspectInfo({
    required this.planet1,
    required this.planet2,
    required this.aspect,
    required this.orb,
    required this.symbol,
    required this.angle,
  });

  Map<String, dynamic> toJson() => {
        'planet1': planet1,
        'planet2': planet2,
        'aspect': aspect,
        'orb': orb,
        'symbol': symbol,
        'angle': angle,
      };
}
