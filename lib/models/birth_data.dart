import 'package:astreal/models/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import 'package:astreal/models/planet_position.dart';
import 'package:intl/intl.dart';
import 'package:sweph/sweph.dart';

class BirthData {
  final String id;
  final String name;
  final DateTime birthDate;
  final String birthPlace;
  final String? notes;
  final double latitude;
  final double longitude;
  final DateTime createdAt; // 添加建立時間字段

  BirthData({
    required this.id,
    required this.name,
    required this.birthDate,
    required this.birthPlace,
    this.notes,
    required this.latitude,
    required this.longitude,
    DateTime? createdAt,
  }) : this.createdAt = createdAt ?? DateTime.now();

  List<PlanetPosition>? planets;
  HouseCuspData? houses;
  List<AspectInfo>? aspects;

  // 從 JSON 創建 BirthData
  factory BirthData.fromJson(Map<String, dynamic> json) {
    return BirthData(
      id: json['id'] as String,
      name: json['name'] as String,
      birthDate: DateTime.parse(json['birthDate'] as String),
      birthPlace: json['birthPlace'] as String,
      notes: json['notes'] as String?,
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'birthDate': DateFormat('yyyy-MM-dd HH:mm').format(birthDate),
      'birthPlace': birthPlace,
      'notes': notes,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': DateFormat('yyyy-MM-dd HH:mm:ss').format(createdAt),
    };
  }

  // 創建副本並更新部分屬性
  BirthData copyWith({
    String? id,
    String? name,
    DateTime? birthDate,
    String? birthPlace,
    String? notes,
    double? latitude,
    double? longitude,
    DateTime? createdAt,
  }) {
    return BirthData(
      id: id ?? this.id,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      birthPlace: birthPlace ?? this.birthPlace,
      notes: notes ?? this.notes,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
