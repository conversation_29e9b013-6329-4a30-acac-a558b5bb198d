/// 複製星盤信息的選項
class CopyOptions {
  /// 是否包含基本資料（姓名、出生日期、地點等）
  final bool includeBasicInfo;
  
  /// 是否包含行星位置
  final bool includePlanetPositions;
  
  /// 是否包含行星尊貴力量
  final bool includePlanetDignities;
  
  /// 是否包含行星日夜區分
  final bool includePlanetSectStatus;
  
  /// 是否包含宮主星
  final bool includeHouseRulers;
  
  /// 是否包含宮位位置
  final bool includeHousePositions;
  
  /// 是否包含相位
  final bool includeAspects;
  
  /// 是否包含互容接納
  final bool includeReceptions;
  
  /// 是否包含元素統計
  final bool includeElementStats;
  
  /// 是否包含特殊點（阿拉伯點）
  final bool includeArabicPoints;
  
  /// 是否使用美化格式（添加分隔線、縮進等）
  final bool usePrettyFormat;

  /// 建構函數
  const CopyOptions({
    this.includeBasicInfo = false,
    this.includePlanetPositions = true,
    this.includePlanetDignities = false,
    this.includePlanetSectStatus = false,
    this.includeHouseRulers = false,
    this.includeHousePositions = false,
    this.includeAspects = true,
    this.includeReceptions = true,
    this.includeElementStats = false,
    this.includeArabicPoints = true,
    this.usePrettyFormat = false,
  });

  /// 創建一個全選的選項
  factory CopyOptions.all() => const CopyOptions();

  /// 創建一個只包含基本信息的選項
  factory CopyOptions.basicOnly() => const CopyOptions(
        includePlanetDignities: false,
        includePlanetSectStatus: false,
        includeHouseRulers: false,
        includeReceptions: false,
        includeElementStats: false,
        includeArabicPoints: false,
      );

  /// 創建一個只包含行星位置的選項
  factory CopyOptions.planetsOnly() => const CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: true,
        includePlanetDignities: false,
        includePlanetSectStatus: false,
        includeHouseRulers: false,
        includeHousePositions: false,
        includeAspects: false,
        includeReceptions: false,
        includeElementStats: false,
        includeArabicPoints: false,
      );

  /// 創建一個只包含相位的選項
  factory CopyOptions.aspectsOnly() => const CopyOptions(
        includeBasicInfo: true,
        includePlanetPositions: false,
        includePlanetDignities: false,
        includePlanetSectStatus: false,
        includeHouseRulers: false,
        includeHousePositions: false,
        includeAspects: true,
        includeReceptions: true,
        includeElementStats: false,
        includeArabicPoints: false,
      );

  /// 創建一個複製品的選項，但可以修改某些屬性
  CopyOptions copyWith({
    bool? includeBasicInfo,
    bool? includePlanetPositions,
    bool? includePlanetDignities,
    bool? includePlanetSectStatus,
    bool? includeHouseRulers,
    bool? includeHousePositions,
    bool? includeAspects,
    bool? includeReceptions,
    bool? includeElementStats,
    bool? includeArabicPoints,
    bool? usePrettyFormat,
  }) {
    return CopyOptions(
      includeBasicInfo: includeBasicInfo ?? this.includeBasicInfo,
      includePlanetPositions: includePlanetPositions ?? this.includePlanetPositions,
      includePlanetDignities: includePlanetDignities ?? this.includePlanetDignities,
      includePlanetSectStatus: includePlanetSectStatus ?? this.includePlanetSectStatus,
      includeHouseRulers: includeHouseRulers ?? this.includeHouseRulers,
      includeHousePositions: includeHousePositions ?? this.includeHousePositions,
      includeAspects: includeAspects ?? this.includeAspects,
      includeReceptions: includeReceptions ?? this.includeReceptions,
      includeElementStats: includeElementStats ?? this.includeElementStats,
      includeArabicPoints: includeArabicPoints ?? this.includeArabicPoints,
      usePrettyFormat: usePrettyFormat ?? this.usePrettyFormat,
    );
  }
}
