import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/astrology_constants.dart';

/// 宮位系統（House System）列舉
enum HouseSystem {
  equal, // A, E
  alcabitius, // <PERSON>
  campanus, // C
  gauquelinSectors, // G
  horizon<PERSON>zimuth, // H
  koch, // K
  morinus, // M
  porphyry, // O
  placidus, // P
  regiomontanus, // R
  topocentric, // T
  krusinskiPisaGoelzer, // U
  equalVehlow, // V
  wholeSign, // W
  meridianHouses, // X
  apcHouses // Y
}

/// House system 字母對應
const Map<String, HouseSystem> houseSystemMap = {
  'A': HouseSystem.equal,
  'E': HouseSystem.equal,
  'B': HouseSystem.alcabitius,
  'C': HouseSystem.campanus,
  'G': HouseSystem.gauquelinSectors,
  'H': HouseSystem.horizonAzimuth,
  'K': HouseSystem.koch,
  'M': HouseSystem.morinus,
  'O': HouseSystem.porphyry,
  'P': HouseSystem.placidus,
  'R': HouseSystem.regiomontanus,
  'T': HouseSystem.topocentric,
  'U': HouseSystem.krusinskiPisaGoelzer,
  'V': HouseSystem.equalVehlow,
  'W': HouseSystem.wholeSign,
  'X': HouseSystem.meridianHouses,
  'Y': HouseSystem.apcHouses,
};

/// HouseSystem 擴展功能
extension HouseSystemExtension on HouseSystem {
  /// 中文顯示名稱
  String get displayName {
    switch (this) {
      case HouseSystem.equal:
        return '等分宮位制 (Equal)';
      case HouseSystem.alcabitius:
        return '阿爾卡比提烏斯制 (Alcabitius)';
      case HouseSystem.campanus:
        return '坎帕納斯制 (Campanus)';
      case HouseSystem.gauquelinSectors:
        return '高克蘭區域 (Gauquelin Sectors)';
      case HouseSystem.horizonAzimuth:
        return '地平方位系統 (Horizon/Azimuth)';
      case HouseSystem.koch:
        return '科赫制 (Koch)';
      case HouseSystem.morinus:
        return '莫里納斯制 (Morinus)';
      case HouseSystem.porphyry:
        return '波耳弗里制 (Porphyry)';
      case HouseSystem.placidus:
        return '普拉西德制 (Placidus)';
      case HouseSystem.regiomontanus:
        return '雷基蒙塔納斯制 (Regiomontanus)';
      case HouseSystem.topocentric:
        return '托波森特制 (Topocentric)';
      case HouseSystem.krusinskiPisaGoelzer:
        return '克魯辛斯基-比薩-戈爾策制 (Krusinski-Pisa-Goelzer)';
      case HouseSystem.equalVehlow:
        return '等分威洛制 (Equal Vehlow)';
      case HouseSystem.wholeSign:
        return '整宮制 (Whole Sign)';
      case HouseSystem.meridianHouses:
        return '子午線系統 (Meridian Houses)';
      case HouseSystem.apcHouses:
        return 'APC 宮位系統';
    }
  }

  /// 取得對應縮寫（反推字母）
  String get abbreviation {
    for (final entry in houseSystemMap.entries) {
      if (entry.value == this) {
        return entry.key;
      }
    }
    return '';
  }
}

/// 反查：由字母找 HouseSystem
HouseSystem? houseSystemFromAbbreviation(String abbr) {
  return houseSystemMap[abbr.toUpperCase()];
}

/// 反查：由中文名稱找 HouseSystem
HouseSystem? houseSystemFromDisplayName(String name) {
  return HouseSystem.values.firstWhere(
    (hs) => hs.displayName == name,
    orElse: () => throw Exception('找不到對應的宮位系統'),
  );
}

Map<String, bool> planetVisibilityHome = {
  '太陽': true,
  '月亮': true,
  '水星': true,
  '金星': true,
  '火星': true,
  '木星': true,
  '土星': true,
  '天王星': true,
  '海王星': true,
  '冥王星': true,
  '上升': false,
  '中天': false,
  '下降': false,
  '天底': false,
  '北交點': false,
  '南交點': false,
  '莉莉絲': false,
  '凱龍星': false,
  '人龍星': false,
  '穀神星': false,
  '智神星': false,
  '婚神星': false,
  '灶神星': false,
};

Map<String, double> aspectOrbsHome = {
  '合相': 3.0,
  '六分相': 3.0,
  '四分相': 3.0,
  '三分相': 3.0,
  '對分相': 3.0,
};

/// 星盤設定類
class ChartSettings {
  // 宮位系統
  String houseSystem;

  // 行星顯示設定
  Map<String, bool> planetVisibility;

  // 相位容許度設定
  Map<String, double> aspectOrbs;

  // 相位顏色設定
  Map<String, Color> aspectColors;

  // 行星顏色設定
  Map<String, Color> planetColors;

  ChartSettings({
    this.houseSystem = 'Placidus',
    Map<String, bool>? planetVisibility,
    Map<String, double>? aspectOrbs,
    Map<String, Color>? aspectColors,
    Map<String, Color>? planetColors,
  })  : planetVisibility = planetVisibility ??
            {
              '太陽': true,
              '月亮': true,
              '水星': true,
              '金星': true,
              '火星': true,
              '木星': true,
              '土星': true,
              '天王星': true,
              '海王星': true,
              '冥王星': true,
              '上升': true,
              '中天': true,
              '下降': false,
              '天底': false,
              '北交點': false,
              '南交點': false,
              '莉莉絲': false,
              '凱龍星': false,
              '人龍星': false,
              '穀神星': false,
              '智神星': false,
              '婚神星': false,
              '灶神星': false,
              '幸運點': false,
              '精神點': false,
              '旺點': false,
            },
        aspectOrbs = aspectOrbs ??
            {
              '合相': 8.0,
              '六分相': 4.0,
              '四分相': 8.0,
              '三分相': 8.0,
              '對分相': 8.0,
            },
        aspectColors = aspectColors ??
            {
              '合相': Colors.red,
              '六分相': Colors.green,
              '四分相': Colors.red,
              '三分相': Colors.green,
              '對分相': const Color(0xFF0A0AFD),
            },
        planetColors = planetColors ?? _getPlanetColorsFromConstants();

  // 從 JSON 創建設定
  factory ChartSettings.fromJson(Map<String, dynamic> json) {
    return ChartSettings(
      houseSystem: json['houseSystem'] as String,
      planetVisibility: Map<String, bool>.from(json['planetVisibility'] as Map),
      aspectOrbs: Map<String, double>.from(
        (json['aspectOrbs'] as Map).map(
          (key, value) {
            // 確保相位容許度值為整數且不超過30
            int intValue = (value as num).toInt();
            if (intValue > 30) intValue = 30;
            if (intValue < 0) intValue = 0;
            return MapEntry(key, intValue.toDouble());
          },
        ),
      ),
      aspectColors: Map<String, Color>.from(
        (json['aspectColors'] as Map).map(
          (key, value) => MapEntry(key, Color(value as int)),
        ),
      ),
      planetColors: Map<String, Color>.from(
        (json['planetColors'] as Map).map(
          (key, value) => MapEntry(key, Color(value as int)),
        ),
      ),
    );
  }

  // 轉換為 JSON
  Map<String, dynamic> toJson() {
    // 創建一個新的 Map 來存儲行星顏色
    final Map<String, int> planetColorValues = {};

    // 使用 AstrologyConstants.PLANETS 中的顏色
    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      planetColorValues[name] = color.value;
    }

    return {
      'houseSystem': houseSystem,
      'planetVisibility': planetVisibility,
      'aspectOrbs': aspectOrbs,
      'aspectColors': aspectColors.map(
        (key, value) => MapEntry(key, value.value),
      ),
      'planetColors': planetColorValues,
    };
  }

  // 保存設定到 SharedPreferences
  Future<void> saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(toJson());
    await prefs.setString('chart_settings', jsonString);
  }

  // 從 AstrologyConstants.PLANETS 中獲取行星顏色
  static Map<String, Color> _getPlanetColorsFromConstants() {
    final Map<String, Color> colors = {};

    for (final planet in AstrologyConstants.PLANETS) {
      final name = planet['name'] as String;
      final color = planet['color'] as Color;
      colors[name] = color;
    }

    return colors;
  }

  // 從 SharedPreferences 加載設定
  static Future<ChartSettings> loadFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final String? settingsJson = prefs.getString('chart_settings');

    if (settingsJson != null) {
      try {
        // 將 JSON 字符串轉換為 Map
        final Map<String, dynamic> jsonMap = jsonDecode(settingsJson);
        return ChartSettings.fromJson(jsonMap);
      } catch (e) {
        debugPrint('加載星盤設定時出錯: $e');
        return ChartSettings();
      }
    }

    return ChartSettings();
  }
}
