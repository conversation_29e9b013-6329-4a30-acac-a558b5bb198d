import 'package:sweph/sweph.dart';

import '../models/chart_data.dart';

/// 星盤計算參數類
/// 用於封裝星盤計算所需的各種參數，減少方法間的參數傳遞
class ChartCalculationParams {
  /// 星盤數據
  final ChartData chartData;
  
  /// 緯度（如果為null，則使用主要人物的緯度）
  final double? latitude;
  
  /// 經度（如果為null，則使用主要人物的經度）
  final double? longitude;
  
  /// 宮位數據（如果為null，則會計算）
  final HouseCuspData? housesData;
  
  /// 是否計算阿拉伯點
  final bool calculateArabicPoints;
  
  /// 行星可見性設定
  final Map<String, bool>? planetVisibility;
  
  /// 相位容許度設定
  final Map<String, double>? aspectOrbs;
  
  /// 有效緯度（計算得出）
  late final double effectiveLatitude;
  
  /// 有效經度（計算得出）
  late final double effectiveLongitude;

  ChartCalculationParams({
    required this.chartData,
    this.latitude,
    this.longitude,
    this.housesData,
    this.calculateArabicPoints = true,
    this.planetVisibility,
    this.aspectOrbs,
  }) {
    // 初始化有效緯度和經度
    effectiveLatitude = latitude ?? chartData.primaryPerson.latitude;
    effectiveLongitude = longitude ?? chartData.primaryPerson.longitude;
  }
}
