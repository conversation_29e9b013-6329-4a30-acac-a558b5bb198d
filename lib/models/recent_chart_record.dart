import 'package:astreal/models/chart_type.dart';

/// 最近使用的星盤記錄
class RecentChartRecord {
  final String id; // 唯一識別碼
  final ChartType chartType; // 星盤類型
  final String primaryPersonId; // 主要人物ID
  final String? secondaryPersonId; // 次要人物ID（可選）
  final DateTime? specificDate; // 特定日期（可選）
  final DateTime usedAt; // 使用時間
  final bool isFavorite; // 是否收藏

  RecentChartRecord({
    required this.id,
    required this.chartType,
    required this.primaryPersonId,
    this.secondaryPersonId,
    this.specificDate,
    required this.usedAt,
    this.isFavorite = false,
  });

  /// 從JSON創建記錄
  factory RecentChartRecord.fromJson(Map<String, dynamic> json) {
    return RecentChartRecord(
      id: json['id'],
      chartType: ChartType.values.firstWhere(
        (type) => type.toString() == json['chartType'],
        orElse: () => ChartType.natal,
      ),
      primaryPersonId: json['primaryPersonId'],
      secondaryPersonId: json['secondaryPersonId'],
      specificDate: json['specificDate'] != null
          ? DateTime.parse(json['specificDate'])
          : null,
      usedAt: DateTime.parse(json['usedAt']),
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chartType': chartType.toString(),
      'primaryPersonId': primaryPersonId,
      'secondaryPersonId': secondaryPersonId,
      'specificDate': specificDate?.toIso8601String(),
      'usedAt': usedAt.toIso8601String(),
      'isFavorite': isFavorite,
    };
  }

  /// 創建一個新的記錄，但更新收藏狀態
  RecentChartRecord copyWithFavorite(bool isFavorite) {
    return RecentChartRecord(
      id: id,
      chartType: chartType,
      primaryPersonId: primaryPersonId,
      secondaryPersonId: secondaryPersonId,
      specificDate: specificDate,
      usedAt: usedAt,
      isFavorite: isFavorite,
    );
  }

  /// 創建一個新的記錄，但更新使用時間
  RecentChartRecord copyWithNewUsedAt() {
    return RecentChartRecord(
      id: id,
      chartType: chartType,
      primaryPersonId: primaryPersonId,
      secondaryPersonId: secondaryPersonId,
      specificDate: specificDate,
      usedAt: DateTime.now(),
      isFavorite: isFavorite,
    );
  }
}
