import 'package:astreal/models/chart_type.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// ChartPageViewModel is a specialized ViewModel for the ChartPage
/// It handles UI-specific logic and state for the ChartPage
class ChartPageViewModel extends ChangeNotifier {
  final ChartViewModel _chartViewModel;
  late TabController _tabController;

  // Constructor
  ChartPageViewModel(this._chartViewModel, TickerProvider vsync) {
    // 如果是法達盤，增加法達盤標籤頁
    int tabCount = 6;
    if (_chartViewModel.chartType == ChartType.firdaria) {
      tabCount = 7;
    }
    _tabController = TabController(length: tabCount, vsync: vsync);
  }

  // Getters
  TabController get tabController => _tabController;
  ChartViewModel get chartViewModel => _chartViewModel;

  // Dispose method
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Factory method to create from context
  static ChartPageViewModel of(BuildContext context, TickerProvider vsync) {
    return ChartPageViewModel(
      Provider.of<ChartViewModel>(context, listen: false),
      vsync,
    );
  }

  // Show email input dialog
  Future<Map<String, dynamic>?> showEmailInputDialog(BuildContext context) async {
    final TextEditingController controller = TextEditingController();
    bool useHtml = true; // Default to HTML format

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('發送星盤數據'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('請輸入接收星盤數據的郵箱地址:'),
                  const SizedBox(height: 16),
                  TextField(
                    controller: controller,
                    decoration: const InputDecoration(
                      labelText: '郵箱地址',
                      hintText: '<EMAIL>',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    autofocus: true,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Checkbox(
                        value: useHtml,
                        onChanged: (value) {
                          setState(() {
                            useHtml = value ?? true;
                          });
                        },
                      ),
                      const Text('使用美化格式 (HTML)'),
                      const Tooltip(
                        message: '使用 HTML 格式可以讓郵件內容更加美觀，但某些郵件客戶端可能不支持',
                        child: Icon(Icons.info_outline, size: 16),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (controller.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('請輸入郵箱地址')),
                      );
                      return;
                    }
                    Navigator.of(context).pop({
                      'email': controller.text.trim(),
                      'useHtml': useHtml,
                    });
                  },
                  child: const Text('發送'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Show PDF options dialog
  Future<String?> showPdfOptionsDialog(BuildContext context) async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('生成 PDF 報告'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text('請選擇操作:')],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop('preview'),
              child: const Text('預覽'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop('share'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('分享'),
            ),
          ],
        );
      },
    );
  }

  // Show chart type selection dialog
  Future<ChartType?> showChartTypeSelectionDialog(BuildContext context, ChartType currentType) async {
    return showDialog<ChartType>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('選擇星盤類型'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: ChartType.values.map((ChartType type) {
                return ListTile(
                  leading: Icon(
                    type == currentType ? Icons.check : Icons.circle_outlined,
                    color: type == currentType ? Colors.blue : null,
                  ),
                  title: Text(type.name),
                  onTap: () {
                    Navigator.of(context).pop(type);
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }
}
