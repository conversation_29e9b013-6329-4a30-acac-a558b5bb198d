import 'dart:math';

import 'package:flutter/material.dart';

import '../models/aspect_info.dart';
import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/chart_type.dart';
import '../models/planet_position.dart';
import '../utils/astrology_calculator.dart';

class DivinationAnalysisViewModel extends ChangeNotifier {
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // 卜卦結果
  Map<String, dynamic>? _divinationResult;
  Map<String, dynamic>? get divinationResult => _divinationResult;

  // 占星卜卦結果
  Map<String, dynamic>? _astrologicalDivinationResult;
  Map<String, dynamic>? get astrologicalDivinationResult => _astrologicalDivinationResult;

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置錯誤訊息
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  // 進行卜卦分析
  Future<void> performDivination(String question) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 模擬卜卦過程
      await Future.delayed(const Duration(seconds: 2));

      // 生成卜卦結果
      _divinationResult = _generateDivinationResult(question);

      setLoading(false);
      notifyListeners();
    } catch (e) {
      setLoading(false);
      setErrorMessage('卜卦分析時出錯: $e');
    }
  }

  // 進行占星卜卦分析
  Future<void> performAstrologicalDivination(String question) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 模擬卜卦過程
      await Future.delayed(const Duration(seconds: 2));

      // 生成占星卜卦結果
      _astrologicalDivinationResult = await _generateAstrologicalDivinationResult(question);

      setLoading(false);
      notifyListeners();
    } catch (e) {
      setLoading(false);
      setErrorMessage('占星卜卦分析時出錯: $e');
    }
  }

  // 生成卜卦結果
  Map<String, dynamic> _generateDivinationResult(String question) {
    final random = Random();

    // 生成六爻
    final List<int> yao = List.generate(6, (_) => random.nextInt(2));

    // 計算上卦和下卦
    final upperTrigram = _calculateTrigram(yao.sublist(0, 3));
    final lowerTrigram = _calculateTrigram(yao.sublist(3));

    // 確定卦象
    final hexagramIndex = upperTrigram * 8 + lowerTrigram;
    final hexagram = _getHexagramInfo(hexagramIndex);

    // 生成變爻
    final List<int> changingLines = [];
    for (int i = 0; i < 6; i++) {
      if (random.nextInt(4) == 0) {  // 25% 的機率產生變爻
        changingLines.add(i);
      }
    }

    // 如果沒有變爻，隨機選一個
    if (changingLines.isEmpty) {
      changingLines.add(random.nextInt(6));
    }

    // 計算變卦
    final List<int> changedYao = List.from(yao);
    for (final line in changingLines) {
      changedYao[line] = 1 - changedYao[line];  // 0 變 1，1 變 0
    }

    final changedUpperTrigram = _calculateTrigram(changedYao.sublist(0, 3));
    final changedLowerTrigram = _calculateTrigram(changedYao.sublist(3));
    final changedHexagramIndex = changedUpperTrigram * 8 + changedLowerTrigram;
    final changedHexagram = _getHexagramInfo(changedHexagramIndex);

    // 生成解釋
    final interpretation = _generateInterpretation(hexagram, changedHexagram, changingLines);

    return {
      'question': question,
      'hexagram': hexagram,
      'changingLines': changingLines,
      'changedHexagram': changedHexagram,
      'interpretation': interpretation,
      'yao': yao,
      'changedYao': changedYao,
    };
  }

  // 計算卦象（將三個爻轉換為 0-7 的數字）
  int _calculateTrigram(List<int> trigram) {
    return trigram[0] * 4 + trigram[1] * 2 + trigram[2];
  }

  // 獲取卦象信息
  Map<String, dynamic> _getHexagramInfo(int index) {
    final hexagrams = [
      {'name': '乾', 'meaning': '天', 'nature': '剛健中正', 'description': '元亨利貞。象徵天行健，君子以自強不息。'},
      {'name': '坤', 'meaning': '地', 'nature': '柔順厚德', 'description': '元亨利牝馬之貞。象徵地勢坤，君子以厚德載物。'},
      {'name': '屯', 'meaning': '雷水', 'nature': '始生難', 'description': '元亨利貞，勿用有攸往，利建侯。象徵雲雷屯，君子以經綸。'},
      {'name': '蒙', 'meaning': '山水', 'nature': '蒙昧啟蒙', 'description': '亨。匪我求童蒙，童蒙求我。象徵山下出泉，蒙。君子以果行育德。'},
      {'name': '需', 'meaning': '水天', 'nature': '等待時機', 'description': '有孚，光亨，貞吉。利涉大川。象徵雲上於天，需。君子以飲食宴樂。'},
      {'name': '訟', 'meaning': '天水', 'nature': '爭訟', 'description': '有孚窒惕，中吉，終凶。利見大人，不利涉大川。象徵天與水違行，訟。君子以作事謀始。'},
      {'name': '師', 'meaning': '地水', 'nature': '眾眾', 'description': '貞丈人吉，無咎。象徵地中有水，師。君子以容民畜眾。'},
      {'name': '比', 'meaning': '水地', 'nature': '親比', 'description': '吉。原筮元永貞，無咎。不寧方來，後夫凶。象徵地上有水，比。先王以建萬國，親諸侯。'},
      {'name': '小畜', 'meaning': '風天', 'nature': '蓄養', 'description': '亨。密雲不雨，自我西郊。象徵風行天上，小畜。君子以懿文德。'},
      {'name': '履', 'meaning': '天澤', 'nature': '踐履', 'description': '履虎尾，不咥人，亨。象徵上天下澤，履。君子以辨上下，定民志。'},
      {'name': '泰', 'meaning': '地天', 'nature': '通泰', 'description': '小往大來，吉亨。象徵天地交，泰。后以財成天地之道，輔相天地之宜，以左右民。'},
      {'name': '否', 'meaning': '天地', 'nature': '閉塞', 'description': '否之匪人，不利君子貞，大往小來。象徵天地不交，否。君子以儉德辟難，不可榮以祿。'},
      {'name': '同人', 'meaning': '天火', 'nature': '和同', 'description': '同人于野，亨。利涉大川，利君子貞。象徵天與火，同人。君子以類族辨物。'},
      {'name': '大有', 'meaning': '火天', 'nature': '大有', 'description': '元亨。象徵火在天上，大有。君子以遏惡揚善，順天休命。'},
      {'name': '謙', 'meaning': '地山', 'nature': '謙虛', 'description': '亨，君子有終。象徵地中有山，謙。君子以裒多益寡，稱物平施。'},
      {'name': '豫', 'meaning': '雷地', 'nature': '喜悅', 'description': '利建侯行師。象徵雷出地奮，豫。先王以作樂崇德，殷薦之上帝，以配祖考。'},
      {'name': '隨', 'meaning': '澤雷', 'nature': '隨順', 'description': '元亨利貞，無咎。象徵澤中有雷，隨。君子以向晦入宴息。'},
      {'name': '蠱', 'meaning': '山風', 'nature': '蠱惑', 'description': '元亨，利涉大川。先甲三日，後甲三日。象徵山下有風，蠱。君子以振民育德。'},
      {'name': '臨', 'meaning': '地澤', 'nature': '臨視', 'description': '元亨利貞。至于八月有凶。象徵澤上有地，臨。君子以教思無窮，容保民無疆。'},
      {'name': '觀', 'meaning': '風地', 'nature': '觀察', 'description': '盥而不薦，有孚顒若。象徵風行地上，觀。先王以省方觀民設教。'},
      {'name': '噬嗑', 'meaning': '火雷', 'nature': '咬合', 'description': '亨。利用獄。象徵雷電噬嗑。先王以明罰敕法。'},
      {'name': '賁', 'meaning': '山火', 'nature': '文飾', 'description': '亨。小利有攸往。象徵山下有火，賁。君子以明庶政，無敢折獄。'},
      {'name': '剝', 'meaning': '山地', 'nature': '剝落', 'description': '不利有攸往。象徵山附於地，剝。上以厚下，安宅。'},
      {'name': '復', 'meaning': '地雷', 'nature': '復歸', 'description': '亨。出入無疾，朋來無咎。反復其道，七日來復，利有攸往。象徵雷在地中，復。先王以至日閉關，商旅不行，後不省方。'},
      {'name': '無妄', 'meaning': '天雷', 'nature': '無妄', 'description': '元亨利貞。其匪正有眚，不利有攸往。象徵天下雷行，物與無妄。先王以茂對時育萬物。'},
      {'name': '大畜', 'meaning': '山天', 'nature': '大畜', 'description': '利貞，不家食吉，利涉大川。象徵天在山中，大畜。君子以多識前言往行，以畜其德。'},
      {'name': '頤', 'meaning': '山雷', 'nature': '頤養', 'description': '貞吉。觀頤，自求口實。象徵山下有雷，頤。君子以慎言語，節飲食。'},
      {'name': '大過', 'meaning': '澤風', 'nature': '大過', 'description': '棟橈，利有攸往，亨。象徵澤滅木，大過。君子以獨立不懼，遯世無悶。'},
      {'name': '坎', 'meaning': '水', 'nature': '陷險', 'description': '習坎，有孚，維心亨，行有尚。象徵水洊至，習坎。君子以常德行，習教事。'},
      {'name': '離', 'meaning': '火', 'nature': '麗明', 'description': '利貞，亨。畜牝牛，吉。象徵明兩作，離。大人以繼明照于四方。'},
      {'name': '咸', 'meaning': '澤山', 'nature': '感應', 'description': '亨，利貞，取女吉。象徵山上有澤，咸。君子以虛受人。'},
      {'name': '恆', 'meaning': '雷風', 'nature': '恆久', 'description': '亨，無咎，利貞，利有攸往。象徵雷風，恆。君子以立不易方。'},
      {'name': '遯', 'meaning': '天山', 'nature': '遯退', 'description': '亨，小利貞。象徵天下有山，遯。君子以遠小人，不惡而嚴。'},
      {'name': '大壯', 'meaning': '雷天', 'nature': '大壯', 'description': '利貞。象徵雷在天上，大壯。君子以非禮弗履。'},
      {'name': '晉', 'meaning': '火地', 'nature': '晉升', 'description': '康侯用錫馬蕃庶，晝日三接。象徵明出地上，晉。君子以自昭明德。'},
      {'name': '明夷', 'meaning': '地火', 'nature': '明傷', 'description': '利艱貞。象徵明入地中，明夷。君子以莊敬內省。'},
      {'name': '家人', 'meaning': '風火', 'nature': '家人', 'description': '利女貞。象徵風自火出，家人。君子以言有物而行有恆。'},
      {'name': '睽', 'meaning': '火澤', 'nature': '睽違', 'description': '小事吉。象徵上火下澤，睽。君子以同而異。'},
      {'name': '蹇', 'meaning': '水山', 'nature': '蹇難', 'description': '利西南，不利東北；利見大人，貞吉。象徵山上有水，蹇。君子以反身修德。'},
      {'name': '解', 'meaning': '雷水', 'nature': '解散', 'description': '利西南，無所往，其來復吉。有攸往，夙吉。象徵雷雨作，解。君子以赦過宥罪。'},
      {'name': '損', 'meaning': '山澤', 'nature': '損益', 'description': '有孚，元吉，無咎，可貞，利有攸往。曷之用，二簋可用享。象徵山下有澤，損。君子以懲忿窒欲。'},
      {'name': '益', 'meaning': '風雷', 'nature': '益進', 'description': '利有攸往，利涉大川。象徵風雷，益。君子以見善則遷，有過則改。'},
      {'name': '夬', 'meaning': '澤天', 'nature': '決斷', 'description': '揚于王庭，孚號，有厲，告自邑，不利即戎，利有攸往。象徵澤上於天，夬。君子以施祿及下，居德則忌。'},
      {'name': '姤', 'meaning': '天風', 'nature': '遇合', 'description': '女壯，勿用取女。象徵天下有風，姤。后以施命誥四方。'},
      {'name': '萃', 'meaning': '澤地', 'nature': '萃聚', 'description': '亨。王假有廟，利見大人，亨，利貞。用大牲吉，利有攸往。象徵澤上於地，萃。君子以除戎器，戒不虞。'},
      {'name': '升', 'meaning': '地風', 'nature': '升進', 'description': '元亨，用見大人，勿恤，南征吉。象徵地中生木，升。君子以順德，積小以高大。'},
      {'name': '困', 'meaning': '澤水', 'nature': '困窮', 'description': '亨，貞，大人吉，無咎，有言不信。象徵澤無水，困。君子以致命遂志。'},
      {'name': '井', 'meaning': '水風', 'nature': '井道', 'description': '改邑不改井，無喪無得，往來井井。汔至，亦未繘井，羸其瓶，凶。象徵木上有水，井。君子以勞民勸相。'},
      {'name': '革', 'meaning': '澤火', 'nature': '革新', 'description': '己日乃孚。元亨利貞，悔亡。象徵澤中有火，革。君子以治歷明時。'},
      {'name': '鼎', 'meaning': '火風', 'nature': '鼎器', 'description': '元吉，亨。象徵木上有火，鼎。君子以正位凝命。'},
      {'name': '震', 'meaning': '雷', 'nature': '震動', 'description': '亨。震來虩虩，笑言啞啞。震驚百里，不喪匕鬯。象徵洊雷，震。君子以恐懼修省。'},
      {'name': '艮', 'meaning': '山', 'nature': '止止', 'description': '艮其背，不獲其身，行其庭，不見其人，無咎。象徵兼山，艮。君子以思不出其位。'},
      {'name': '漸', 'meaning': '風山', 'nature': '漸進', 'description': '女歸吉，利貞。象徵山上有木，漸。君子以居賢德善俗。'},
      {'name': '歸妹', 'meaning': '雷澤', 'nature': '歸女', 'description': '征凶，無攸利。象徵澤上有雷，歸妹。君子以永終知敝。'},
      {'name': '豐', 'meaning': '雷火', 'nature': '豐盈', 'description': '亨，王假之，勿憂，宜日中。象徵雷電皆至，豐。君子以折獄致刑。'},
      {'name': '旅', 'meaning': '火山', 'nature': '旅行', 'description': '小亨，旅貞吉。象徵山上有火，旅。君子以明慎用刑，而不留獄。'},
      {'name': '巽', 'meaning': '風', 'nature': '順入', 'description': '小亨，利攸往，利見大人。象徵隨風，巽。君子以申命行事。'},
      {'name': '兌', 'meaning': '澤', 'nature': '悅順', 'description': '亨，利貞。象徵麗澤，兌。君子以朋友講習。'},
      {'name': '渙', 'meaning': '風水', 'nature': '渙散', 'description': '亨，王假有廟，利涉大川，利貞。象徵風行水上，渙。先王以享于帝，立廟。'},
      {'name': '節', 'meaning': '水澤', 'nature': '節制', 'description': '亨。苦節不可貞。象徵澤上有水，節。君子以制數度，議德行。'},
      {'name': '中孚', 'meaning': '風澤', 'nature': '中信', 'description': '豚魚吉，利涉大川，利貞。象徵澤上有風，中孚。君子以議獄緩死。'},
      {'name': '小過', 'meaning': '雷山', 'nature': '小過', 'description': '亨，利貞，可小事，不可大事。飛鳥遺之音，不宜上宜下，大吉。象徵山上有雷，小過。君子以行過乎恭，喪過乎哀，用過乎儉。'},
      {'name': '既濟', 'meaning': '水火', 'nature': '既濟', 'description': '亨小，利貞。初吉終亂。象徵水在火上，既濟。君子以思患而豫防之。'},
      {'name': '未濟', 'meaning': '火水', 'nature': '未濟', 'description': '亨，小狐汔濟，濡其尾，無攸利。象徵火在水上，未濟。君子以慎辨物居方。'},
    ];

    return hexagrams[index % hexagrams.length];
  }

  // 生成卜卦解釋
  String _generateInterpretation(Map<String, dynamic> hexagram, Map<String, dynamic> changedHexagram, List<int> changingLines) {
    final StringBuffer buffer = StringBuffer();

    // 本卦解釋
    buffer.writeln('【本卦：${hexagram['name']}卦】');
    buffer.writeln('${hexagram['name']}卦代表「${hexagram['meaning']}」，性質為「${hexagram['nature']}」。');
    buffer.writeln('${hexagram['description']}');
    buffer.writeln();

    // 變爻解釋
    buffer.writeln('【變爻】');
    if (changingLines.isEmpty) {
      buffer.writeln('無變爻，本卦穩定，情況不會有太大變化。');
    } else {
      buffer.writeln('第 ${changingLines.map((i) => i + 1).join('、')} 爻發生變化。');

      for (final line in changingLines) {
        final position = line + 1;
        final lineType = line < 3 ? '下卦' : '上卦';
        final linePosition = line % 3 + 1;

        buffer.write('第 $position 爻（$lineType第$linePosition爻）：');

        switch (position) {
          case 1:
            buffer.writeln('代表事情的開始或基礎，變化表示基礎不穩或有新的開始。');
            break;
          case 2:
            buffer.writeln('代表內在或準備階段，變化表示內心或準備工作有所調整。');
            break;
          case 3:
            buffer.writeln('代表轉折或突破，變化表示即將有重要轉變。');
            break;
          case 4:
            buffer.writeln('代表外在環境或他人影響，變化表示外部條件將有所改變。');
            break;
          case 5:
            buffer.writeln('代表主導位置或關鍵因素，變化表示核心要素將發生變化。');
            break;
          case 6:
            buffer.writeln('代表結果或未來趨勢，變化表示最終結果可能與預期不同。');
            break;
        }
      }
    }
    buffer.writeln();

    // 變卦解釋
    buffer.writeln('【變卦：${changedHexagram['name']}卦】');
    buffer.writeln('${changedHexagram['name']}卦代表「${changedHexagram['meaning']}」，性質為「${changedHexagram['nature']}」。');
    buffer.writeln('${changedHexagram['description']}');
    buffer.writeln();

    // 綜合解釋
    buffer.writeln('【綜合解釋】');
    buffer.writeln('從${hexagram['name']}卦變化為${changedHexagram['name']}卦，表示情況將從「${hexagram['nature']}」轉變為「${changedHexagram['nature']}」。');

    // 根據卦象組合給出具體建議
    buffer.writeln(_getAdviceBasedOnHexagrams(hexagram, changedHexagram));

    return buffer.toString();
  }

  // 根據卦象組合給出建議
  String _getAdviceBasedOnHexagrams(Map<String, dynamic> hexagram, Map<String, dynamic> changedHexagram) {
    final originalName = hexagram['name'];
    final changedName = changedHexagram['name'];

    // 根據卦象組合給出建議
    if (originalName == '乾' && changedName == '坤') {
      return '從乾卦（天）變為坤卦（地），表示從積極主動轉為謙遜包容。建議此時不宜剛強進取，應當退守靜待，以柔克剛。';
    } else if (originalName == '坤' && changedName == '乾') {
      return '從坤卦（地）變為乾卦（天），表示從謙遜包容轉為積極主動。時機已成熟，可以開始行動，積極進取。';
    } else if ((originalName == '泰' && changedName == '否') || (originalName == '否' && changedName == '泰')) {
      return '泰卦與否卦互為變卦，代表通泰與閉塞的轉換。當前形勢正在發生重大變化，應當順應時勢，靈活調整策略。';
    } else {
      // 隨機生成一些通用建議
      final advices = [
        '目前的情況正在轉變中，保持耐心和觀察力，等待適當的時機再行動。',
        '變化即將到來，做好準備迎接新的機遇和挑戰。',
        '當前的困難是暫時的，堅持下去會看到轉機。',
        '不要被表面現象迷惑，深入思考事情的本質和發展趨勢。',
        '適當調整自己的心態和行動方式，以適應新的形勢。',
        '尋求可靠的人給予建議和支持，共同面對挑戰。',
        '保持謙虛和開放的態度，接納不同的觀點和可能性。',
        '相信自己的直覺，但也要理性分析和判斷。',
        '不要急於求成，循序漸進，穩步前行。',
        '關注細節，但不要忽視整體局勢和長遠目標。',
      ];

      final random = Random();
      return advices[random.nextInt(advices.length)];
    }
  }

  // 生成占星卜卦解釋
  String _generateAstrologicalInterpretation(
    String question,
    PlanetPosition sun,
    PlanetPosition moon,
    PlanetPosition mercury,
    PlanetPosition venus,
    PlanetPosition mars,
    PlanetPosition jupiter,
    PlanetPosition saturn,
    PlanetPosition ascendant,
    PlanetPosition mc,
    List<AspectInfo> aspects,
  ) {
    final StringBuffer buffer = StringBuffer();
    final random = Random();

    // 問題分析
    buffer.writeln('【問題分析】');
    buffer.writeln('您的問題：$question');
    buffer.writeln('根據當前星象，我們將從各個行星的位置和相位關係來分析您的問題。');
    buffer.writeln();

    // 主要行星解釋
    buffer.writeln('【主要行星解釋】');

    // // 太陽解釋
    // buffer.writeln('太陽（代表意識、目標和生命力）：');
    // buffer.writeln('位於${sun.sign}的第${sun.house}宮，${_getPlanetPositionInterpretation(sun)}');
    // buffer.writeln();
    //
    // // 月亮解釋
    // buffer.writeln('月亮（代表情緒、直覺和無意識）：');
    // buffer.writeln('位於${moon.sign}的第${moon.house}宮，${_getPlanetPositionInterpretation(moon)}');
    // buffer.writeln();
    //
    // // 水星解釋
    // buffer.writeln('水星（代表思考、溝通和分析）：');
    // buffer.writeln('位於${mercury.sign}的第${mercury.house}宮，${_getPlanetPositionInterpretation(mercury)}');
    // buffer.writeln();
    //
    // // 金星解釋
    // buffer.writeln('金星（代表愛情、價值觀和和調）：');
    // buffer.writeln('位於${venus.sign}的第${venus.house}宮，${_getPlanetPositionInterpretation(venus)}');
    // buffer.writeln();
    //
    // // 火星解釋
    // buffer.writeln('火星（代表行動、勇氣和欲望）：');
    // buffer.writeln('位於${mars.sign}的第${mars.house}宮，${_getPlanetPositionInterpretation(mars)}');
    // buffer.writeln();
    //
    // // 木星解釋
    // buffer.writeln('木星（代表擴張、成長和機會）：');
    // buffer.writeln('位於${jupiter.sign}的第${jupiter.house}宮，${_getPlanetPositionInterpretation(jupiter)}');
    // buffer.writeln();
    //
    // // 土星解釋
    // buffer.writeln('土星（代表限制、結構和責任）：');
    // buffer.writeln('位於${saturn.sign}的第${saturn.house}宮，${_getPlanetPositionInterpretation(saturn)}');
    // buffer.writeln();
    //
    // // 重要相位解釋
    // if (aspects.isNotEmpty) {
    //   buffer.writeln('【重要相位解釋】');
    //
    //   // 篩選重要相位（只選擇主要行星之間的相位）
    //   final significantAspects = aspects.where((a) =>
    //     _isSignificantPlanet(a.planet1.name) &&
    //     _isSignificantPlanet(a.planet2.name) &&
    //     (a.angle == 0 || a.angle == 60 || a.angle == 90 || a.angle == 120 || a.angle == 180)
    //   ).toList();
    //
    //   // 隨機選擇最多5個相位進行解釋
    //   significantAspects.shuffle(random);
    //   final aspectsToInterpret = significantAspects.take(5).toList();
    //
    //   for (final aspect in aspectsToInterpret) {
    //     buffer.writeln('${aspect.planet1.name}與${aspect.planet2.name}形成${aspect.shortZh}：');
    //     buffer.writeln(_getAspectInterpretation(aspect));
    //     buffer.writeln();
    //   }
    // }
    //
    // // 綜合解釋
    // buffer.writeln('【綜合解釋】');
    //
    // // 根據不同的問題類型提供不同的解釋
    // if (_isRelationshipQuestion(question)) {
    //   buffer.writeln(_getRelationshipInterpretation(venus, mars, moon, jupiter, aspects));
    // } else if (_isCareerQuestion(question)) {
    //   buffer.writeln(_getCareerInterpretation(sun, saturn, jupiter, mc, aspects));
    // } else if (_isHealthQuestion(question)) {
    //   buffer.writeln(_getHealthInterpretation(sun, moon, mars, saturn, aspects));
    // } else if (_isFinancialQuestion(question)) {
    //   buffer.writeln(_getFinancialInterpretation(venus, jupiter, saturn, aspects));
    // } else {
    //   buffer.writeln(_getGeneralInterpretation(sun, moon, mercury, aspects));
    // }
    //
    // // 建議與行動方向
    // buffer.writeln('【建議與行動方向】');
    // buffer.writeln(_getAdviceBasedOnPlanets(sun, moon, mercury, venus, mars, jupiter, saturn));

    return buffer.toString();
  }

  // 生成占星卜卦結果
  Future<Map<String, dynamic>> _generateAstrologicalDivinationResult(String question) async {
    final random = Random();
    final now = DateTime.now();

    // 創建當前時間的星盤數據
    final chartData = ChartData(
      chartType: ChartType.mundane,
      primaryPerson: BirthData(
        id: 'divination_${now.millisecondsSinceEpoch}',
        name: '占星卜卦',
        birthDate: now,
        birthPlace: '當前位置',
        latitude: 25.0, // 預設值，實際應用中應使用用戶當前位置
        longitude: 121.0, // 預設值，實際應用中應使用用戶當前位置
      ),
    );

    // 計算行星位置
    final calculatedChartData = await AstrologyCalculator.calculatePlanetPositionsForChart(
      chartData,
    );

    // 獲取行星位置和相位
    final planets = calculatedChartData.planets ?? [];
    final aspects = calculatedChartData.aspects ?? [];

    // 選擇關鍵行星（太陽、月亮、上升點）
    final sun = planets.firstWhere((p) => p.name == '太陽', orElse: () => planets.first);
    final moon = planets.firstWhere((p) => p.name == '月亮', orElse: () => planets.first);
    final mercury = planets.firstWhere((p) => p.name == '水星', orElse: () => planets.first);

    // 獲取關鍵相位
    final significantAspects = aspects.where((a) =>
      (a.planet1.name == '太陽' || a.planet2.name == '太陽' ||
       a.planet1.name == '月亮' || a.planet2.name == '月亮' ||
       a.planet1.name == '水星' || a.planet2.name == '水星') &&
      (a.angle == 0 || a.angle == 60 || a.angle == 90 || a.angle == 120 || a.angle == 180)
    ).toList();

    // 生成解釋
    // final interpretation = _generateAstrologicalInterpretation(
    //   question,
    //   sun,
    //   moon,
    //   mercury,
    //   significantAspects
    // );

    return {
      'question': question,
      'timestamp': now.toString(),
      'sun': {
        'sign': sun.sign,
        'house': sun.house,
        'longitude': sun.longitude,
      },
      'moon': {
        'sign': moon.sign,
        'house': moon.house,
        'longitude': moon.longitude,
      },
      'mercury': {
        'sign': mercury.sign,
        'house': mercury.house,
        'longitude': mercury.longitude,
      },
      'aspects': significantAspects.map((a) => {
        'planet1': a.planet1.name,
        'planet2': a.planet2.name,
        'aspect': a.aspect,
        'angle': a.angle,
      }).toList(),
      'planets': planets.map((p) => {
        'name': p.name,
        'sign': p.sign,
        'house': p.house,
        'longitude': p.longitude,
      }).toList(),
      // 'interpretation': interpretation,
    };
  }

  // 獲取卦象的圖形表示
  List<String> getHexagramSymbol(List<int> yao) {
    final List<String> symbol = [];
    for (int i = 0; i < 6; i++) {
      if (yao[i] == 0) {  // 陰爻
        symbol.add('- -');
      } else {  // 陽爻
        symbol.add('───');
      }
    }
    return symbol.reversed.toList();  // 從下到上顯示
  }
}
