import 'package:astreal/utils/logger_utils.dart';
import 'package:flutter/foundation.dart';

import '../models/chart_data.dart';
import '../models/chart_type.dart';
import '../services/ai_interpretation_service.dart';
import '../utils/date_formatter.dart';


class ChartInterpretationViewModel extends ChangeNotifier {
  bool _isLoading = false;
  String _interpretation = '';
  String _error = '';

  bool get isLoading => _isLoading;

  String get interpretation => _interpretation;

  String get error => _error;

  Future<void> getInterpretation(ChartData chartData) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final prompt = _generatePrompt(chartData);
      logger.d("prompt\n$prompt");
      _interpretation = await AIInterpretationService.getInterpretation(
        chartData,
        prompt,
      );

    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> getDualChartInterpretation(
      ChartData chartData
  ) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final prompt = _generateDualChartPrompt(chartData);
      logger.d("prompt\n$prompt");
      _interpretation =
          await AIInterpretationService.getDualChartInterpretation(
        prompt,
      );
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  String _generatePrompt(ChartData chartData) {
    String specificPrompt = '';

    switch (chartData.chartType) {
      case ChartType.natal:
        specificPrompt = '''
請特別關注：
- 上升星座和命主星的影響
- 重要行星在宮位的意義
- 整體性格和人生方向的分析
- 個人天賦與潛能的展現
- 生命重要課題和使命
''';
        break;
      case ChartType.secondaryProgression:
        specificPrompt = '''
請特別關注：
- 一天代表一年的推進軌跡
- 內在心理成長的脈絡
- 重要行星推運帶來的影響
- 長期性格發展的方向
''';
        break;
      case ChartType.tertiaryProgression:
        specificPrompt = '''
請特別關注：
- 一天代表一個月的變化
- 短期心理狀態的轉變
- 近期重要事件的影響
''';
        break;
      case ChartType.solarArcDirection:
        specificPrompt = '''
請特別關注：
- 所有行星的統一推進
- 重大人生轉折點
- 關鍵時期的發展機會
''';
        break;
      case ChartType.transit:
        specificPrompt = '''
請特別關注：
- 當下行星運行的影響
- 時機的把握與選擇
- 外在環境帶來的機會與挑戰
''';
        break;
      case ChartType.synastry:
        specificPrompt = '''
請特別關注：
- 兩張星盤間的互動關係
- 雙方能量的相互影響
- 關係發展的潛在機遇和挑戰
- 重要相位的影響
''';
        break;
      case ChartType.composite:
        specificPrompt = '''
請特別關注：
- 兩人星盤中點的意義
- 關係本質的展現
- 共同發展的方向
''';
        break;
      case ChartType.davison:
        specificPrompt = '''
請特別關注：
- 關係的時空意義
- 兩人緣分的特質
- 關係發展的整體脈絡
''';
        break;
      case ChartType.solarReturn:
        specificPrompt = '''
請特別關注：
- 未來一年的整體運勢
- 生日年度的重要議題
- 年度發展機會與挑戰
- 關鍵時期的判斷
''';
        break;
      case ChartType.lunarReturn:
        specificPrompt = '''
請特別關注：
- 當月整體運勢走向
- 短期發展重點
- 月度關鍵時機
''';
        break;

      case '諧波盤':
        specificPrompt = '''
請特別關注：
- 特定諧波的意義
- 隱藏模式的顯現
- 深層議題的解析
''';
        break;
      default:
        specificPrompt = '''
請提供一般性的星盤解讀：
- 重要星座和行星位置
- 主要相位的影響
- 整體運勢分析
''';
    }

    return '''
分析以下${chartData.chartType.displayName}並提供詳細的占星解讀：

1. 基本信息：
- 姓名：${chartData.primaryPerson.name}
- 出生時間：${chartData.primaryPerson.birthDate != null ? DateFormatter.formatDateTime(chartData.primaryPerson.birthDate) : '未知'}
- 出生地點：${chartData.primaryPerson.birthPlace}
- 星盤類型：${chartData.chartType.displayName}

2. 行星位置：
${chartData.planets?.map((p) => '- ${p.name}：位於 ${p.sign}，在第 ${p.house} 宮${p.longitudeSpeed < 0 ? '（逆行）' : ''}').join('\n')}

3. 重要相位：
${chartData.aspects!.isNotEmpty ? chartData.aspects?.map((a) => '- ${a.planet1.name} ${a.aspect} ${a.planet2.name}（容許度：${a.orb.toStringAsFixed(2)}°）').join('\n') : '無重要相位'}

4. 宮位數據：
${chartData.houses?.cusps.asMap().entries.map((e) {
      final houseNumber = e.key + 1; // 因為宮位從第1宮開始
      return '- 第$houseNumber宮：${_getZodiacSign(e.value)} ${_formatDegree(e.value % 30)}';
    }).join('\n')}

$specificPrompt

請用繁體中文回答。
''';
  }

  String _generateDualChartPrompt(
      ChartData chartData
  ) {
    return '''
分析以下${chartData.chartType.displayName}數據並提供占星解讀：

第一位基本信息：
- 姓名：${chartData.primaryPerson.name}
- 出生時間：${chartData.primaryPerson.birthDate != null ? DateFormatter.formatDateTime(chartData.primaryPerson.birthDate) : '未知'}
- 出生地點：${chartData.primaryPerson.birthPlace}

第二位基本信息：
- 姓名：${chartData.secondaryPerson?.name}
- 出生時間：${chartData.secondaryPerson?.birthDate != null ? DateFormatter.formatDateTime(chartData.secondaryPerson!.birthDate) : '未知'}
- 出生地點：${chartData.secondaryPerson?.birthPlace}

${chartData.secondaryPerson?.name}的行星落入${chartData.primaryPerson.name}的宮位
${chartData.planets!.map((p) => '- ${p.name}：位於 ${p.sign}，在第 ${p.house} 宮${p.longitudeSpeed < 0 ? '（逆行）' : ''}').join('\n')}

兩人之間的相位：
${chartData.primaryPerson.name} vs ${chartData.secondaryPerson?.name}
${chartData.aspects!.isNotEmpty ? chartData.aspects!.map((a) => '- ${a.planet1.name} ${a.aspect} ${a.planet2.name}').join('\n') : '無重要相位'}

請用繁體中文回答。
''';
  }

  String _getZodiacSign(double longitude) {
    final signs = [
      '白羊',
      '金牛',
      '雙子',
      '巨蟹',
      '獅子',
      '處女',
      '天秤',
      '天蠍',
      '射手',
      '摩羯',
      '水瓶',
      '雙魚'
    ];
    final signIndex = (longitude / 30).floor() % 12;
    return signs[signIndex];
  }

  String _formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();
    final int sec = ((minDouble - min) * 60).round();
    return '$deg°$min\'$sec"';
  }
}
