import 'dart:convert';

import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/services/chart_service.dart';
import 'package:astreal/services/equinox_solstice_service.dart';
import 'package:astreal/utils/astrology_calculator.dart' as astro;
import 'package:astreal/utils/logger_utils.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/astrology_constants.dart';
import '../models/chart_settings.dart';

/// 首頁視圖模型，處理首頁相關的業務邏輯和數據
class HomeViewModel extends ChangeNotifier {
  // 今日星相數據
  List<Map<String, dynamic>> _recentAspects = [];
  bool _isLoading = true;
  String _selectedLocation = '台北市';
  double _latitude = 25.0330;
  double _longitude = 121.5654;

  // 選中的人物資訊
  BirthData? _selectedPerson;
  List<BirthData> _birthDataList = [];
  bool _isLoadingBirthData = false;

  // 行運盤資訊
  Map<String, dynamic> _transitInfo = {};
  bool _isLoadingTransitInfo = false;
  List<dynamic>? _transitAspects;
  bool _isTransitInfoExpanded = true; // 行運盤影響是否展開
  Map<String, bool> _aspectExpandedStates = {}; // 跟踪每個相位的折疊狀態

  // 節氣相關資訊
  SeasonData? _nextSeason;
  bool _isLoadingNextSeason = false;
  final EquinoxSolsticeService _equinoxSolsticeService = EquinoxSolsticeService();

  // Getters
  List<Map<String, dynamic>> get recentAspects => _recentAspects;
  bool get isLoading => _isLoading;
  String get selectedLocation => _selectedLocation;
  double get latitude => _latitude;
  double get longitude => _longitude;
  BirthData? get selectedPerson => _selectedPerson;
  List<BirthData> get birthDataList => _birthDataList;
  bool get isLoadingBirthData => _isLoadingBirthData;
  Map<String, dynamic> get transitInfo => _transitInfo;
  bool get isLoadingTransitInfo => _isLoadingTransitInfo;
  List<dynamic>? get transitAspects => _transitAspects;
  bool get isTransitInfoExpanded => _isTransitInfoExpanded;
  Map<String, bool> get aspectExpandedStates => _aspectExpandedStates;
  SeasonData? get nextSeason => _nextSeason;
  bool get isLoadingNextSeason => _isLoadingNextSeason;

  // 切換行運盤影響展開/折疊狀態
  void toggleTransitInfoExpanded() {
    _isTransitInfoExpanded = !_isTransitInfoExpanded;
    notifyListeners();
  }

  // 切換單個相位的展開/折疊狀態
  void toggleAspectExpanded(String aspectId) {
    _aspectExpandedStates[aspectId] = !(_aspectExpandedStates[aspectId] ?? false);
    notifyListeners();
  }

  // 獲取相位的展開/折疊狀態
  bool isAspectExpanded(String aspectId) {
    return _aspectExpandedStates[aspectId] ?? false; // 默認折疊
  }

  // 生成相位的唯一ID
  String getAspectId(AspectInfo aspect) {
    return '${aspect.planet1.name}_${aspect.aspect}_${aspect.planet2.name}';
  }

  // 構造函數
  HomeViewModel() {
    _init();
  }

  // 初始化
  void _init() {
    loadBirthData();
    loadRecentAspects();
    loadNextSeason();
  }

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置出生數據加載狀態
  void setLoadingBirthData(bool loading) {
    _isLoadingBirthData = loading;
    notifyListeners();
  }

  // 設置行運盤加載狀態
  void setLoadingTransitInfo(bool loading) {
    _isLoadingTransitInfo = loading;
    notifyListeners();
  }

  // 更新位置信息
  void updateLocation(String location, double latitude, double longitude) {
    _selectedLocation = location;
    _latitude = latitude;
    _longitude = longitude;
    notifyListeners();
    loadRecentAspects();
    loadNextSeason(); // 位置變更時重新載入節氣資訊
  }

  // 載入下一個節氣
  Future<void> loadNextSeason() async {
    try {
      _isLoadingNextSeason = true;
      notifyListeners();

      final nextSeason = await _equinoxSolsticeService.getNextSeason(
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _selectedPerson,
      );

      _nextSeason = nextSeason;
      _isLoadingNextSeason = false;
      notifyListeners();
    } catch (e) {
      logger.e('載入下一個節氣時出錯: $e');
      _isLoadingNextSeason = false;
      notifyListeners();
    }
  }

  // 加載今日星相數據
  Future<void> loadRecentAspects() async {
    try {
      setLoading(true);

      // 獲取當前日期
      final now = DateTime.now();
      final List<Map<String, dynamic>> aspects = [];

      // 只計算今日星相
      final date = now;
      final chartData = ChartData(
        specificDate: date,
        chartType: ChartType.mundane,
        primaryPerson: BirthData(
          id: 'mundane_${date.millisecondsSinceEpoch}',
          name: '天象',
          birthDate: date,
          birthPlace: _selectedLocation,
          latitude: _latitude,
          longitude: _longitude,
        ),
      );

      // 計算行星位置
      ChartData data =
          await astro.AstrologyCalculator.calculatePlanetPositionsForChart(
        chartData,
        planetVisibility: planetVisibilityHome,
      );
      final planets = data.planets;

      // 計算相位
      final aspectsForDay =
          astro.AstrologyCalculator.calculateAspects(planets!);

      // 只保留重要的相位（合相、對分相、三分相、四分相）
      final importantAspects = aspectsForDay.where((aspect) {
        return aspect.aspect == '合相' ||
            aspect.aspect == '對分相' ||
            aspect.aspect == '三分相' ||
            aspect.aspect == '四分相';
      }).toList();

      // 添加今日星相數據
      aspects.add({
        'date': date,
        'aspects': importantAspects,
        'signChanges': <Map<String, dynamic>>[],
        'planets': planets,
      });

      _recentAspects = aspects;
      setLoading(false);
    } catch (e) {
      logger.e('載入近期星象時出錯: $e');
      setLoading(false);
    }
  }

  // 從 SharedPreferences 加載出生數據
  Future<void> loadBirthData() async {
    try {
      setLoadingBirthData(true);

      final prefs = await SharedPreferences.getInstance();
      final String? birthDataJson = prefs.getString('birthDataList');

      if (birthDataJson != null) {
        final List<dynamic> decodedData = jsonDecode(birthDataJson);
        final loadedData =
            decodedData.map((item) => BirthData.fromJson(item)).toList();

        _birthDataList = loadedData;

        // 如果有保存的選中人物ID，則恢復選中狀態
        final String? selectedPersonId = prefs.getString('selectedPersonId');
        if (selectedPersonId != null && loadedData.isNotEmpty) {
          _selectedPerson = loadedData.firstWhere(
            (person) => person.id == selectedPersonId,
            orElse: () => loadedData.first,
          );
          // 計算行運資訊和行運盤資訊
          calculateTransitInfo();
        } else if (loadedData.isNotEmpty) {
          // 如果沒有保存的選中人物，則默認選中第一個
          _selectedPerson = loadedData.first;
          // 計算行運資訊和行運盤資訊
          calculateTransitInfo();
        }
      }

      setLoadingBirthData(false);
    } catch (e) {
      logger.e('加載出生數據時出錯: $e');
      setLoadingBirthData(false);
    }
  }

  // 保存選中的人物ID
  Future<void> saveSelectedPersonId() async {
    if (_selectedPerson != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedPersonId', _selectedPerson!.id);
    }
  }

  // 設置選中的人物
  void setSelectedPerson(BirthData person) {
    _selectedPerson = person;
    saveSelectedPersonId();
    calculateTransitInfo();
    notifyListeners();
  }

  // 計算行運盤資訊
  Future<void> calculateTransitInfo() async {
    if (_selectedPerson == null) return;

    setLoadingTransitInfo(true);

    try {
      final now = DateTime.now();

      // 創建行運盤數據
      ChartData transitChartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: _selectedPerson!,
        specificDate: now,
      );

      // 計算行運盤
      transitChartData = await ChartService().calculatePlanetPositionsForChart(
        transitChartData,
        planetVisibility: planetVisibilityHome,
        aspectOrbs: aspectOrbsHome,
      );

      // 取得行運盤的相位
      final aspects = transitChartData.aspects;

      // 取得重要的相位（合相、對分相、三分相、四分相）
      final importantAspects = aspects?.where((aspect) {
        return aspect.aspect == '合相' ||
            aspect.aspect == '對分相' ||
            aspect.aspect == '三分相' ||
            aspect.aspect == '四分相';
      }).toList();

      // 按重要性排序相位
      importantAspects?.sort((a, b) {
        final importanceA =
            getAspectImportance(a.aspect, a.planet1.name, a.planet2.name);
        final importanceB =
            getAspectImportance(b.aspect, b.planet1.name, b.planet2.name);
        return importanceB.compareTo(importanceA);
      });

      // 更新行運盤資訊
      _transitInfo = {
        'date': now,
        'planets': transitChartData.planets,
      };
      _transitAspects = importantAspects;

      // 初始化相位的展開/折疊狀態
      if (importantAspects != null) {
        for (final aspect in importantAspects) {
          final aspectId = getAspectId(aspect);
          if (!_aspectExpandedStates.containsKey(aspectId)) {
            _aspectExpandedStates[aspectId] = false; // 默認折疊
          }
        }
      }

      setLoadingTransitInfo(false);
    } catch (e) {
      logger.e('計算行運盤資訊時出錯: $e');
      setLoadingTransitInfo(false);
    }
  }

  // 更新出生數據
  Future<void> updateBirthData(BirthData updatedData) async {
    // 更新列表中的數據
    final index = _birthDataList.indexWhere((data) => data.id == updatedData.id);
    if (index != -1) {
      _birthDataList[index] = updatedData;

      // 如果更新的是當前選中的人物，也更新選中的人物
      if (_selectedPerson?.id == updatedData.id) {
        _selectedPerson = updatedData;
      }

      // 儲存更新後的出生數據列表
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> encodedData =
          _birthDataList.map((data) => data.toJson()).toList();
      await prefs.setString('birthDataList', jsonEncode(encodedData));

      // 重新計算行運盤資訊
      if (_selectedPerson?.id == updatedData.id) {
        calculateTransitInfo();
      }

      notifyListeners();
    }
  }

  // 創建今日星相的 ChartData
  ChartData createTodayChartData() {
    if (_recentAspects.isEmpty) {
      throw Exception('沒有今日星相數據');
    }

    final dayAspects = _recentAspects[0];
    final date = dayAspects['date'] as DateTime;
    final planets = dayAspects['planets'] as List;

    // 創建 ChartData 對象
    return ChartData(
      specificDate: date,
      chartType: ChartType.mundane,
      primaryPerson: BirthData(
        id: 'mundane_${date.millisecondsSinceEpoch}',
        name: '今日星相',
        birthDate: date,
        birthPlace: _selectedLocation,
        latitude: _latitude,
        longitude: _longitude,
      ),
    );
  }

  // 創建本命盤的 ChartData
  ChartData createNatalChartData() {
    if (_selectedPerson == null) {
      throw Exception('沒有選中的人物');
    }

    return ChartData(
      chartType: ChartType.natal,
      primaryPerson: _selectedPerson!,
    );
  }

  // 創建行運盤的 ChartData
  ChartData createTransitChartData() {
    if (_selectedPerson == null) {
      throw Exception('沒有選中的人物');
    }

    return ChartData(
      chartType: ChartType.transit,
      primaryPerson: _selectedPerson!,
      specificDate: DateTime.now(),
    );
  }

  // 獲取相位的重要性等級
  int getAspectImportance(String aspect, String planet1, String planet2) {
    final majorPersonalPoints = ['太陽', '月亮'];
    final innerPlanets = ['太陽', '月亮', '水星', '金星', '火星'];
    final outerPlanets = ['木星', '土星', '天王星', '海王星', '冥王星'];

    // 相位基礎重要性（合相 最高，六分相 最低）
    int aspectWeight;
    switch (aspect) {
      case '合相':
        aspectWeight = 4;
        break;
      case '對分相':
        aspectWeight = 3;
        break;
      case '四分相':
        aspectWeight = 2;
        break;
      case '三分相':
        aspectWeight = 2;
        break;
      case '六分相':
        aspectWeight = 1;
        break;
      default:
        aspectWeight = 1;
    }

    // 是否包含重要個人點（上升、中天等）
    bool involvesMajorPoint = majorPersonalPoints.contains(planet1) ||
        majorPersonalPoints.contains(planet2);

    // 是否為內行星之間的相位（更貼近個人）
    bool isInnerAspect =
        innerPlanets.contains(planet1) && innerPlanets.contains(planet2);

    // 是否為內外行星交會（行運意義強）
    bool isMixedAspect =
        (innerPlanets.contains(planet1) && outerPlanets.contains(planet2)) ||
            (outerPlanets.contains(planet1) && innerPlanets.contains(planet2));

    // 基礎值 +1 若涉及重要點
    int importance = aspectWeight;
    if (involvesMajorPoint) importance += 1;
    if (isMixedAspect) importance += 1;
    if (isInnerAspect) importance += 1;

    // 限制在 1 到 5 之間
    return importance.clamp(1, 5);
  }

  // 獲取宮位的特質描述
  String getHouseDescription(int houseNumber) {
    // 確保宮位編號在 1-12 範圍內
    int house = (houseNumber <= 0 || houseNumber > 12) ? 1 : houseNumber;

    // 使用 ZodiacSymbols 中的宮位描述
    switch (house) {
      case 1:
        return '自我、外表、個性、生命力';
      case 2:
        return '財產、價值觀、資源、安全感';
      case 3:
        return '溝通、短途旅行、兄弟姐妹、早期教育';
      case 4:
        return '家庭、根源、父母、內在情感基礎';
      case 5:
        return '創造力、娛樂、子女、浪漫關係';
      case 6:
        return '健康、工作、日常生活、服務';
      case 7:
        return '關係、婚姻、合作夥伴、公開的敵人';
      case 8:
        return '共享資源、轉變、性、死亡與重生';
      case 9:
        return '高等教育、哲學、長途旅行、信仰';
      case 10:
        return '職業、社會地位、名聲、權威';
      case 11:
        return '友誼、團體、願望、社會理想';
      case 12:
        return '潛意識、秘密、限制、靈性成長';
      default:
        return '宮位特質';
    }
  }

  // 獲取相位的預測描述
  String getAspectForecast(AspectInfo aspect) {
    String planet1 = aspect.planet1.name;
    String planet2 = aspect.planet2.name;
    int house1 = aspect.planet1.house;
    int house2 = aspect.planet2.house;
    String aspectType = aspect.aspect;

    final meaning = aspectDescriptions[aspectType];
    if (meaning == null) return '☁️ 未知相位，請確認資料是否正確。';

    String house1Desc = getHouseDescription(house1);
    String house2Desc = getHouseDescription(house2);
    String houseContext = (house1 == house2)
        ? '「$house1Desc」的主題會被放大強化'
        : '「$house1Desc」與「$house2Desc」之間感受到';

    int importance = getAspectImportance(aspectType, planet1, planet2);
    String importanceTone = importanceToneMap[importance] ?? '';

    final buffer = StringBuffer();
    buffer.writeln('${meaning.symbol} $planet1 與 $planet2 呈現 $aspectType，相位落在第 $house1 宮（$house1Desc）與第 $house2 宮（$house2Desc）之間。');
    buffer.writeln('這段時間，你可能會在 $houseContext${meaning.base}');
    buffer.writeln(meaning.tone);
    buffer.writeln(meaning.advice);
    buffer.writeln(importanceTone);

    return buffer.toString().trimRight();
  }

  // 格式化日期
  String formatDate(DateTime date) {
    return '${date.month}月${date.day}日';
  }

  // 格式化日期時間
  String formatDateTime(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
