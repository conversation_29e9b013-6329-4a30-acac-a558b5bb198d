import 'package:flutter/material.dart';

import '../models/birth_data.dart';
import '../models/firdaria_data.dart';
import '../services/firdaria_service.dart';

/// 法達盤 ViewModel
class FirdariaViewModel extends ChangeNotifier {
  final FirdariaService _firdariaService = FirdariaService();

  /// 出生數據
  BirthData? _birthData;

  /// 法達盤數據
  List<FirdariaData>? _firdariaData;

  /// 是否正在加載
  bool _isLoading = false;

  /// 是否發生錯誤
  bool _hasError = false;

  /// 錯誤信息
  String _errorMessage = '';

  /// 獲取出生數據
  BirthData? get birthData => _birthData;

  /// 獲取法達盤數據
  List<FirdariaData>? get firdariaData => _firdariaData;

  /// 獲取是否正在加載
  bool get isLoading => _isLoading;

  /// 獲取是否發生錯誤
  bool get hasError => _hasError;

  /// 獲取錯誤信息
  String get errorMessage => _errorMessage;

  /// 獲取當前週期
  FirdariaData? get currentPeriod {
    if (_firdariaData == null) return null;
    return _firdariaData!.firstWhere(
      (period) => period.isCurrent,
      orElse: () => _firdariaData!.first,
    );
  }

  /// 獲取當前子週期
  FirdariaSubPeriod? get currentSubPeriod {
    final period = currentPeriod;
    if (period == null) return null;

    return period.subPeriods.firstWhere(
      (subPeriod) => subPeriod.isCurrent,
      orElse: () => period.subPeriods.first,
    );
  }

  /// 計算法達盤數據
  Future<void> calculateFirdaria(BirthData birthData) async {
    print('計算法達盤數據');

    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();

    try {
      _birthData = birthData;

      // 計算法達盤數據
      _firdariaData = await _firdariaService.calculateFirdaria(
        birthData,
        currentDate: DateTime.now(),
      );

      print('法達盤數據計算完成，共 ${_firdariaData?.length} 個週期');
    } catch (e) {
      print('計算法達盤數據時出錯: $e');
      _hasError = true;
      _errorMessage = '計算法達盤數據時出錯: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 清除數據
  void clearData() {
    _birthData = null;
    _firdariaData = null;
    _isLoading = false;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
}
