import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/ai_model.dart';
import '../models/chart_settings.dart';

class SettingsViewModel extends ChangeNotifier {
  static const String _selectedModelKey = 'selected_ai_model';

  // State variables
  bool _isLoading = true;
  String _selectedModelId = 'gemma2-9b-it';
  ChartSettings? _chartSettings;
  late TabController _tabController;

  // AI 模型列表
  final List<AIModel> _models = const [
    AIModel(
      name: 'Gemma2 9B',
      id: 'gemma2-9b-it',
      description: 'Google 的開源模型，速度快，資源佔用少',
    ),
    AIModel(
      name: 'Mixtral 8x7B',
      id: 'mixtral-8x7b-32768',
      description: '強大的開源模型，支援多語言，適合複雜的星盤解讀',
    ),
    AIModel(
      name: 'Claude 3 Opus',
      id: 'claude-3-opus-20240229',
      description: '最新的 Claude 模型，具有強大的推理能力和準確性',
    ),
    AIModel(
      name: 'GPT-4',
      id: 'chatgpt-4o-latest',
      description: 'OpenAI 的頂級模型，提供準確和創新的解讀',
    ),
  ];

  // 宮位系統列表
  final List<String> _houseSystems = [
    'Placidus',
    'Koch',
    'Campanus',
    'Regiomontanus',
    'Equal',
    'Whole Sign',
    'Porphyrius',
    'Meridian',
    'Morinus',
    'Topocentric',
  ];

  // Constructor with initialization
  SettingsViewModel() {
    // Load settings asynchronously without notifying during construction
    _initSettings();
  }

  // Getters
  bool get isLoading => _isLoading;
  String get selectedModelId => _selectedModelId;
  ChartSettings? get chartSettings => _chartSettings;
  TabController get tabController => _tabController;
  List<AIModel> get models => _models;
  List<String> get houseSystems => _houseSystems;

  // Initialize with TabController
  void initTabController(TabController controller) {
    _tabController = controller;
  }

  // Initialize settings without triggering UI updates
  Future<void> _initSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final chartSettings = await ChartSettings.loadFromPrefs();

    _selectedModelId = prefs.getString(_selectedModelKey) ?? 'gemma2-9b-it';
    _chartSettings = chartSettings;
    _isLoading = false;

    // Now it's safe to notify listeners
    notifyListeners();
  }

  // Save selected AI model
  Future<void> saveSelectedModel(String modelId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedModelKey, modelId);

    _selectedModelId = modelId;
    notifyListeners();
  }

  // Save chart settings
  Future<void> saveChartSettings() async {
    if (_chartSettings != null) {
      await _chartSettings!.saveToPrefs();
      notifyListeners();
    }
  }

  // Update house system
  void updateHouseSystem(String newValue) {
    if (_chartSettings != null) {
      _chartSettings!.houseSystem = newValue;
      saveChartSettings();
    }
  }

  // Update planet visibility
  void updatePlanetVisibility(String planet, bool isVisible) {
    if (_chartSettings != null) {
      _chartSettings!.planetVisibility[planet] = isVisible;
      saveChartSettings();
    }
  }

  // Update aspect orb
  void updateAspectOrb(String aspect, double value) {
    if (_chartSettings != null) {
      // 確保值為整數且不超過30
      int intValue = value.toInt();
      if (intValue > 30) intValue = 30;
      if (intValue < 0) intValue = 0;

      _chartSettings!.aspectOrbs[aspect] = intValue.toDouble();
      saveChartSettings();
    }
  }

  /// 清除所有儲存資料與設定
  ///
  /// 清除後會重置所有設定為預設值，並通知其他 ViewModel 更新
  /// 注意：此方法不會直接調用 FilesViewModel 的 clearAllData 方法，
  /// 而是由調用者負責在清除設定後調用該方法。
  Future<void> clearAllSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清除 AI 模型選擇
      await prefs.remove(_selectedModelKey);

      // 清除星盤設定
      await prefs.remove('chart_settings');

      // 清除出生資料列表
      await prefs.remove('birthDataList');
      await prefs.remove('selectedBirthDataId');

      // 清除用戶資訊
      await prefs.remove('user_name');
      await prefs.remove('user_email');
      await prefs.remove('user_phone');
      await prefs.remove('user_contact_method');

      // 清除預約相關資訊
      await prefs.remove('user_contact_method');

      // 清除其他設定
      await prefs.remove('last_viewed_birth_data_id');
      await prefs.remove('selected_chart_type');

      // 重新載入預設設定
      _selectedModelId = 'gemma2-9b-it';
      _chartSettings = ChartSettings(); // 建立預設設定

      // 通知 UI 更新
      notifyListeners();

      debugPrint('成功清除所有設定與儲存資料');
      return Future.value();
    } catch (e) {
      debugPrint('清除設定時出錯: $e');
      return Future.error(e);
    }
  }
}
