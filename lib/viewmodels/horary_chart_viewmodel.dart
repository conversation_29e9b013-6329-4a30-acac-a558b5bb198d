import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/chart_type.dart';
import '../models/divination_record.dart';
import '../models/planet_position.dart';
import '../services/divination_record_service.dart';
import '../utils/astrology_calculator.dart' as astro;
import '../utils/geocoding_service.dart';
import '../utils/logger_utils.dart';

/// 時刻盤視圖模型
/// 用於處理占星卜卦相關的業務邏輯
class HoraryChartViewModel extends ChangeNotifier {
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  // 卜卦結果
  ChartData? _chartData;

  ChartData? get chartData => _chartData;

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置錯誤訊息
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 進行占星卜卦分析
  Future<void> performHoraryDivination({
    required DateTime questionTime,
    required String location,
    required String question,
  }) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 從地址獲取經緯度
      final coordinates =
          await GeocodingService.getCoordinatesFromAddress(location);
      final latitude = coordinates['latitude']!;
      final longitude = coordinates['longitude']!;

      // 創建卜卦時間的星盤數據
      final chartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'horary_${questionTime.millisecondsSinceEpoch}',
          name: '占星卜卦',
          birthDate: questionTime,
          birthPlace: location,
          latitude: latitude,
          longitude: longitude,
          notes: question,
        ),
      );

      // 計算行星位置
      _chartData =
          await astro.AstrologyCalculator.calculatePlanetPositionsForChart(
        chartData,
      );

      // 保存卜卦記錄
      await _saveHoraryRecord(question, location, questionTime);

      setLoading(false);
      notifyListeners();
    } catch (e) {
      logger.e('占星卜卦分析時出錯: $e');
      setLoading(false);
      setErrorMessage('占星卜卦分析時出錯: $e');
    }
  }

  /// 獲取卜卦結果的文本描述
  String getHoraryResultText() {
    if (_chartData == null) {
      return '尚未進行卜卦';
    }

    final StringBuffer buffer = StringBuffer();
    final birthData = _chartData!.primaryPerson;
    final planets = _chartData!.planets ?? [];
    final aspects = _chartData!.aspects ?? [];

    // 基本信息
    buffer.writeln('【占星卜卦】');
    buffer.writeln('問題：${birthData.notes}');
    buffer.writeln('時間：${_formatDateTime(birthData.birthDate)}');
    buffer.writeln('地點：${birthData.birthPlace}');
    buffer.writeln();

    // 上升點和中天
    PlanetPosition? ascendant;
    PlanetPosition? mc;
    for (final planet in planets) {
      if (planet.name == '上升') {
        ascendant = planet;
      } else if (planet.name == '中天') {
        mc = planet;
      }
    }

    if (ascendant != null) {
      buffer.writeln(
          '上升點：${ascendant.sign} ${_formatDegree(ascendant.longitude)}');
    }
    if (mc != null) {
      buffer.writeln('中天：${mc.sign} ${_formatDegree(mc.longitude)}');
    }
    buffer.writeln();

    // 主要行星位置
    buffer.writeln('【主要行星位置】');
    final mainPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];
    for (final planetName in mainPlanets) {
      final planet = planets.firstWhere(
        (p) => p.name == planetName,
        orElse: () => PlanetPosition(
          id: 0,
          name: planetName,
          symbol: '',
          longitude: 0,
          latitude: 0,
          distance: 0,
          longitudeSpeed: 0,
          latitudeSpeed: 0,
          distanceSpeed: 0,
          sign: '',
          house: 0,
        ),
      );

      buffer.writeln(
          '$planetName：${planet.sign} ${_formatDegree(planet.longitude)}，第${planet.house}宮');
    }
    buffer.writeln();

    // 重要相位
    buffer.writeln('【重要相位】');
    final significantAspects = aspects
        .where((a) =>
            (a.angle == 0 ||
                a.angle == 60 ||
                a.angle == 90 ||
                a.angle == 120 ||
                a.angle == 180) &&
            mainPlanets.contains(a.planet1.name) &&
            mainPlanets.contains(a.planet2.name))
        .take(5);

    if (significantAspects.isEmpty) {
      buffer.writeln('無重要相位');
    } else {
      for (final aspect in significantAspects) {
        buffer.writeln(
            '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}');
      }
    }

    return buffer.toString();
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化度數
  String _formatDegree(double longitude) {
    final int degree = longitude.floor() % 30;
    final int minute = ((longitude - longitude.floor()) * 60).round();
    return '$degree°$minute\'';
  }

  /// 保存占星卜卦記錄
  Future<void> _saveHoraryRecord(String question, String location, DateTime timestamp) async {
    try {
      if (_chartData == null) {
        logger.w('無法保存卜卦記錄：無星盤數據');
        return;
      }

      logger.d('開始保存占星卜卦記錄...');

      // 生成記錄ID
      final String recordId = const Uuid().v4();

      // 獲取卜卦結果文本
      final String resultText = getHoraryResultText();
      logger.d('卜卦結果文本長度: ${resultText.length}');

      // 創建卜卦記錄
      final DivinationRecord record = DivinationRecord(
        id: recordId,
        question: question,
        location: location,
        timestamp: timestamp,
        type: 'horary',
        result: resultText,
        chartData: _chartData!.toJson(),
      );

      logger.d('創建記錄完成，正在保存...');

      // 保存記錄
      await DivinationRecordService.saveRecord(record);
      logger.d('占星卜卦記錄已成功保存: $recordId');
    } catch (e) {
      logger.e('保存占星卜卦記錄時出錯: $e');
      logger.e('錯誤堆棧: ${e.toString()}');
    }
  }
}
