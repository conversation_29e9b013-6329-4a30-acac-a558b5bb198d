import 'dart:convert';

import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/birth_data.dart';
import '../utils/csv_helper.dart';
import '../utils/geocoding_service.dart';
import '../utils/logger_utils.dart';

/// 排序方式
enum SortOption {
  nameAsc,
  nameDesc,
  dateNewest,
  dateOldest,
  createdNewest,  // 建立時間（最新）
  createdOldest,  // 建立時間（最舊）
}

/// 檔案頁面的 ViewModel
class FilesViewModel extends ChangeNotifier {
  // 出生資料列表
  List<BirthData> _birthDataList = [];
  List<BirthData> _filteredList = [];
  bool _isLoading = true;

  // 多選模式相關
  bool _isMultiSelectMode = false;
  Set<String> _selectedItems = {};

  // 搜索相關
  final TextEditingController searchController = TextEditingController();

  // 排序相關
  SortOption _currentSortOption = SortOption.dateNewest;

  // 複製相關
  bool _isCopying = false;

  // 當前選擇的個人資料
  BirthData? _currentPerson;

  // Getters
  List<BirthData> get birthDataList => _birthDataList;
  List<BirthData> get filteredList => _filteredList;
  bool get isLoading => _isLoading;
  bool get isMultiSelectMode => _isMultiSelectMode;
  Set<String> get selectedItems => _selectedItems;

  SortOption get currentSortOption => _currentSortOption;
  bool get isCopying => _isCopying;

  // Constructor
  FilesViewModel() {
    _initAsync();
  }

  // 非同步初始化
  void _initAsync() {
    _init().then((_) {
      // 初始化完成
    }).catchError((error) {
      logger.e('初始化錯誤: $error');
    });
  }

  Future<void> _init() async {
    // 從 SharedPreferences 加載排序選項
    await _loadSortOption();

    loadBirthData();
    searchController.addListener(_filterBirthData);
  }

  // 從 SharedPreferences 加載排序選項
  Future<void> _loadSortOption() async {
    final prefs = await SharedPreferences.getInstance();
    final sortOptionIndex = prefs.getInt('sortOption');

    if (sortOptionIndex != null && sortOptionIndex < SortOption.values.length) {
      _currentSortOption = SortOption.values[sortOptionIndex];
    }
  }

  @override
  void dispose() {
    searchController.removeListener(_filterBirthData);
    searchController.dispose();
    super.dispose();
  }

  // 根據搜索文本過濾出生資料
  void _filterBirthData() {
    final query = searchController.text.toLowerCase();

    if (query.isEmpty) {
      _filteredList = List.from(_birthDataList);
    } else {
      _filteredList = _birthDataList.where((data) {
        return data.name.toLowerCase().contains(query) ||
            data.birthPlace.toLowerCase().contains(query) ||
            (data.notes != null && data.notes!.toLowerCase().contains(query));
      }).toList();
    }

    _sortBirthData();
    notifyListeners();
  }

  // 排序出生資料
  void _sortBirthData() {
    switch (_currentSortOption) {
      case SortOption.nameAsc:
        _filteredList.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.nameDesc:
        _filteredList.sort((a, b) => b.name.compareTo(a.name));
        break;
      case SortOption.dateNewest:
        _filteredList.sort((a, b) => b.birthDate.compareTo(a.birthDate));
        break;
      case SortOption.dateOldest:
        _filteredList.sort((a, b) => a.birthDate.compareTo(b.birthDate));
        break;
      case SortOption.createdNewest:
        _filteredList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.createdOldest:
        _filteredList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
    }
    notifyListeners();
  }

  // 設置排序選項
  Future<void> setSortOption(SortOption option) async {
    _currentSortOption = option;
    _sortBirthData();

    // 將排序選項儲存到 SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('sortOption', option.index);
  }

  // 從 SharedPreferences 加載資料
  Future<void> loadBirthData() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final String? birthDataJson = prefs.getString('birthDataList');

      if (birthDataJson != null) {
        final List<dynamic> decodedData = jsonDecode(birthDataJson);
        _birthDataList = decodedData.map((item) => BirthData.fromJson(item)).toList();
        _filteredList = List.from(_birthDataList);
        _sortBirthData();
      }
    } catch (e) {
      logger.e('加載資料時出錯: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 儲存資料到 SharedPreferences
  Future<void> saveBirthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> encodedData =
          _birthDataList.map((data) => data.toJson()).toList();
      await prefs.setString('birthDataList', jsonEncode(encodedData));
    } catch (e) {
      logger.e('儲存資料時出錯: $e');
    }
  }

  /// 清除所有出生資料
  ///
  /// 清除內存中的資料並通知 UI 更新
  void clearAllData() {
    _birthDataList.clear();
    _filteredList.clear();
    _selectedItems.clear();
    _isMultiSelectMode = false;
    _currentPerson = null;
    notifyListeners();
    logger.d('清除所有出生資料成功');
  }

  // 匯出出生資料到 CSV 文件
  Future<String?> exportToCsv() async {
    if (_birthDataList.isEmpty) {
      return null;
    }

    try {
      // 匯出數據
      final filePath = await CsvHelper.exportBirthData(_birthDataList);
      return filePath;
    } catch (e) {
      logger.e('匯出資料時出錯: $e');
      return null;
    }
  }

  // 從 CSV 文件匯入出生資料
  Future<List<BirthData>> importFromCsv() async {
    try {
      // 匯入數據
      final importedData = await CsvHelper.importBirthDataFromCsv();
      return importedData;
    } catch (e) {
      logger.e('匯入資料時出錯: $e');
      return [];
    }
  }

  // 替換現有資料
  Future<void> replaceExistingData(List<BirthData> newData) async {
    _birthDataList = newData;
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
  }

  // 合併到現有資料
  Future<int> mergeWithExistingData(List<BirthData> newData) async {
    // 合併資料，避免 ID 重複
    final existingIds = _birthDataList.map((data) => data.id).toSet();
    final uniqueNewData = newData.where((data) => !existingIds.contains(data.id)).toList();

    _birthDataList.addAll(uniqueNewData);
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();

    return uniqueNewData.length;
  }

  // 添加新的出生資料
  Future<void> addBirthData(BirthData newData) async {
    _birthDataList.add(newData);
    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
  }

  // 更新出生資料
  Future<void> updateBirthData(String id, BirthData updatedData) async {
    // 先找到要更新的項目在原始列表中的索引
    final index = _birthDataList.indexWhere((data) => data.id == id);
    if (index != -1) {
      _birthDataList[index] = updatedData;
      await saveBirthData();
      _filteredList = List.from(_birthDataList);
      _sortBirthData();
    }
  }

  // 刪除出生資料
  Future<void> deleteBirthData(String id) async {
    // 先找到要刪除的項目在原始列表中的索引
    final index = _birthDataList.indexWhere((data) => data.id == id);
    if (index != -1) {
      _birthDataList.removeAt(index);
      await saveBirthData();
      _filteredList = List.from(_birthDataList);
      _sortBirthData();
    }
  }

  // 刪除選中的出生資料
  Future<int> deleteSelectedBirthData() async {
    if (_selectedItems.isEmpty) return 0;

    final selectedCount = _selectedItems.length;
    _birthDataList.removeWhere((data) => _selectedItems.contains(data.id));
    _selectedItems.clear();

    // 如果刪除後沒有項目了，退出多選模式
    if (_birthDataList.isEmpty) {
      _isMultiSelectMode = false;
    }

    await saveBirthData();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();

    return selectedCount;
  }

  // 切換多選模式
  void toggleMultiSelectMode() {
    _isMultiSelectMode = !_isMultiSelectMode;
    if (!_isMultiSelectMode) {
      _selectedItems.clear();
    }
    notifyListeners();
  }

  // 切換項目選中狀態
  void toggleItemSelection(String id) {
    if (_selectedItems.contains(id)) {
      _selectedItems.remove(id);
    } else {
      _selectedItems.add(id);
    }
    notifyListeners();
  }

  // 全選或取消全選
  void toggleSelectAll() {
    if (_selectedItems.length == _birthDataList.length) {
      // 如果已經全選，則取消全選
      _selectedItems.clear();
    } else {
      // 全選所有項目
      _selectedItems = _birthDataList.map((data) => data.id).toSet();
    }
    notifyListeners();
  }

  // 清除搜索
  void clearSearch() {
    searchController.clear();
    _filteredList = List.from(_birthDataList);
    _sortBirthData();
  }

  // 獲取地理編碼
  Future<Map<String, double>> getCoordinatesFromAddress(String address) async {
    return await GeocodingService.getCoordinatesFromAddress(address);
  }

  // 格式化日期時間
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 設置複製狀態
  void setCopying(bool copying) {
    _isCopying = copying;
    notifyListeners();
  }

  // 複製選中的出生資料
  Future<bool> copySelectedBirthData() async {
    if (_selectedItems.isEmpty) return false;

    setCopying(true);

    try {
      final selectedData = _birthDataList.where((data) => _selectedItems.contains(data.id)).toList();
      final StringBuffer text = StringBuffer();

      text.writeln('===== 出生資料 =====');

      for (final data in selectedData) {
        text.writeln('\n姓名: ${data.name}');
        text.writeln('出生日期: ${formatDateTime(data.birthDate)}');
        text.writeln('出生地點: ${data.birthPlace}');
        text.writeln('經度: ${data.longitude.toStringAsFixed(4)}');
        text.writeln('緯度: ${data.latitude.toStringAsFixed(4)}');
        if (data.notes != null && data.notes!.isNotEmpty) {
          text.writeln('備註: ${data.notes}');
        }
        text.writeln('-------------------');
      }

      // 複製到剪貼板
      await FlutterClipboard.copy(text.toString());
      return true;
    } catch (e) {
      logger.e('複製資料時出錯: $e');
      return false;
    } finally {
      setCopying(false);
    }
  }
}
