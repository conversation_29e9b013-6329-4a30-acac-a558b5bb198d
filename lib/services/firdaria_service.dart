import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../constants/astrology_constants.dart';
import '../models/birth_data.dart';
import '../models/firdaria_data.dart';
import 'astrology_service.dart';

/// 法達盤服務類
/// 提供法達盤計算功能
class FirdariaService {
  /// 法達盤週期順序（日盤）
  /// 太陽、金星、水星、月亮、土星、木星、火星、北交點
  static const List<int> _daytimePlanetOrder = [
    AstrologyConstants.SUN,
    AstrologyConstants.VENUS,
    AstrologyConstants.MERCURY,
    AstrologyConstants.MOON,
    AstrologyConstants.SATURN,
    AstrologyConstants.JUPITER,
    AstrologyConstants.MARS,
    10, // 北交點
    60,
  ];

  /// 法達盤週期順序（夜盤）
  /// 月亮、土星、木星、火星、北交點、太陽、金星、水星
  static const List<int> _nighttimePlanetOrder = [
    AstrologyConstants.MOON,
    AstrologyConstants.SATURN,
    AstrologyConstants.JUPITER,
    AstrologyConstants.MARS,
    AstrologyConstants.SUN,
    AstrologyConstants.VENUS,
    AstrologyConstants.MERCURY,
    10, // 北交點
    60,
  ];

  /// 法達盤週期年數
  static const Map<int, int> _planetYears = {
    AstrologyConstants.SUN: 10,
    AstrologyConstants.VENUS: 8,
    AstrologyConstants.MERCURY: 13,
    AstrologyConstants.MOON: 9,
    AstrologyConstants.SATURN: 11,
    AstrologyConstants.JUPITER: 12,
    AstrologyConstants.MARS: 7,
    10: 3, // 北交點
    60: 2, // 北交點
  };

  /// 計算法達盤數據
  ///
  /// [birthData] 出生數據
  /// [isDaytime] 是否為日盤（如果為 null，則根據太陽位置自動判斷）
  /// [currentDate] 當前日期（用於判斷當前週期）
  ///
  /// 返回法達盤數據列表
  Future<List<FirdariaData>> calculateFirdaria(
    BirthData birthData, {
    bool? isDaytime,
    DateTime? currentDate,
  }) async {
    print('計算法達盤數據');

    // 如果沒有指定當前日期，則使用現在的日期
    currentDate ??= DateTime.now();

    // 如果沒有指定是否為日盤，則根據太陽位置自動判斷
    // 這裡簡化處理，實際應該根據太陽和上升點的位置判斷
    isDaytime = await _isDaytimeBirth(birthData);
    print('出生時間是否為白天: $isDaytime');

    // 根據日夜選擇行星順序
    final planetOrder = isDaytime ? _daytimePlanetOrder : _nighttimePlanetOrder;
    print('法達盤行星順序: $planetOrder');

    // 計算法達盤週期
    final periods = <FirdariaData>[];

    // 起始日期為出生日期
    DateTime periodStartDate = birthData.birthDate;

    // 遍歷行星順序
    for (int i = 0; i < planetOrder.length; i++) {
      final planetId = planetOrder[i];
      final years = _planetYears[planetId]!;

      // 計算週期結束日期
      final periodEndDate = DateTime(
        periodStartDate.year + years,
        periodStartDate.month,
        periodStartDate.day,
        periodStartDate.hour,
        periodStartDate.minute,
        periodStartDate.second,
      );

      // 獲取行星信息
      final planetInfo = _getPlanetInfo(planetId);

      // 計算子週期
      final subPeriods = _calculateSubPeriods(
        planetOrder,
        planetId,
        periodStartDate,
        periodEndDate,
        currentDate,
      );

      // 判斷是否為當前週期
      final isCurrent = periodStartDate.isBefore(currentDate) &&
          periodEndDate.isAfter(currentDate);

      // 創建法達盤週期數據
      final period = FirdariaData(
        startDate: periodStartDate,
        endDate: periodEndDate,
        majorPlanetId: planetId,
        majorPlanetName: planetInfo['name'],
        majorPlanetSymbol: planetInfo['symbol'],
        majorPlanetColor: planetInfo['color'],
        subPeriods: subPeriods,
        isCurrent: isCurrent,
      );

      // 添加到列表
      periods.add(period);

      // 更新下一個週期的起始日期
      periodStartDate = periodEndDate;
    }

    print('法達盤數據計算完成，共 ${periods.length} 個週期');
    return periods;
  }

  /// 計算子週期
  List<FirdariaSubPeriod> _calculateSubPeriods(
    List<int> planetOrder,
    int majorPlanetId,
    DateTime periodStartDate,
    DateTime periodEndDate,
    DateTime currentDate,
  ) {
    print('計算子週期，主要行星: $majorPlanetId');

    final subPeriods = <FirdariaSubPeriod>[];
    final totalDays = periodEndDate.difference(periodStartDate).inDays;

    // 子週期起始日期
    DateTime subPeriodStartDate = periodStartDate;

    // 遍歷行星順序，從主要行星開始
    int startIndex = planetOrder.indexOf(majorPlanetId);
    for (int i = 0; i < planetOrder.length; i++) {
      final index = (startIndex + i) % planetOrder.length;
      final subPlanetId = planetOrder[index];

      // 計算子週期天數（按比例分配）
      final subPeriodDays = (totalDays * _planetYears[subPlanetId]!) ~/ 75;

      // 計算子週期結束日期
      final subPeriodEndDate =
          subPeriodStartDate.add(Duration(days: subPeriodDays));

      // 獲取行星信息
      final planetInfo = _getPlanetInfo(subPlanetId);

      if (planetInfo['name'] == '北交點' || planetInfo['name'] == '南交點') {
        i = -1;
        startIndex = 0;
        continue;
      }

      // 判斷是否為當前子週期
      final isCurrent = subPeriodStartDate.isBefore(currentDate) &&
          subPeriodEndDate.isAfter(currentDate);

      // 創建子週期數據
      final subPeriod = FirdariaSubPeriod(
        startDate: subPeriodStartDate,
        endDate: subPeriodEndDate,
        subPlanetId: subPlanetId,
        subPlanetName: planetInfo['name'],
        subPlanetSymbol: planetInfo['symbol'],
        subPlanetColor: planetInfo['color'],
        isCurrent: isCurrent,
      );

      // 添加到列表
      subPeriods.add(subPeriod);
      // 更新下一個子週期的起始日期
      subPeriodStartDate = subPeriodEndDate;
      if (subPeriods.length == 7) {
        break;
      }
    }

    print('子週期計算完成，共 ${subPeriods.length} 個子週期');
    return subPeriods;
  }

  /// 判斷是否為白天出生
  Future<bool> _isDaytimeBirth(BirthData birthData) async {
    // 使用太陽的宮位位置來確定日/夜圖表狀態
    // 計算太陽位置
    // 計算宮位
    HouseCuspData? houses = await AstrologyService().calculateHouses(
      birthData.birthDate,
      birthData.latitude,
      birthData.longitude,
    );

    // 計算行星位置
    final planets = await AstrologyService().calculatePlanetPositions(
      birthData.birthDate,
      housesData: houses,
      latitude: birthData.latitude,
      longitude: birthData.longitude,
    );

    bool isDaytime = false;
    for (var planet in planets) {
      if (planet.name == '太陽') {
        print('太陽宮位: ${planet.house}');
        if (planet.house >= 7 && planet.house <= 12) {
          print('白天出生');
          isDaytime = true;
          continue;
        } else {
          print('晚上出生');
          isDaytime = false;
          continue;
        }
      }
    }
    return isDaytime;
  }

  /// 獲取行星信息
  Map<String, dynamic> _getPlanetInfo(int planetId) {
    // 從 AstrologyConstants.PLANETS 中獲取行星信息
    final planetInfo = AstrologyConstants.PLANETS.firstWhere(
      (planet) => planet['id'] == planetId,
      orElse: () => {
        'id': planetId,
        'name': '未知行星',
        'unicode': '?',
        'symbol': '?',
        'color': Colors.grey,
      },
    );

    return {
      'name': planetInfo['name'],
      'symbol': planetInfo['symbol'],
      'color': planetInfo['color'],
    };
  }
}
