import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/utils/julian_date_utils.dart';
import 'package:astreal/utils/logger_utils.dart';
import 'package:sweph/sweph.dart';

/// 季節節氣類型
enum SeasonType {
  springEquinox('春分', 0.0),    // 太陽進入白羊座 0°
  summerSolstice('夏至', 90.0),  // 太陽進入巨蟹座 0°
  autumnEquinox('秋分', 180.0),  // 太陽進入天秤座 0°
  winterSolstice('冬至', 270.0); // 太陽進入摩羯座 0°

  const SeasonType(this.displayName, this.solarLongitude);

  final String displayName;
  final double solarLongitude;
}

/// 季節節氣數據
class SeasonData {
  final SeasonType seasonType;
  final DateTime dateTime;
  final ChartData? chartData;

  SeasonData({
    required this.seasonType,
    required this.dateTime,
    this.chartData,
  });
}

/// 二分二至計算服務
///
/// 負責計算春分、夏至、秋分、冬至的精確時間，
/// 並生成對應的星盤數據
class EquinoxSolsticeService {

  /// 計算指定年份的四季節氣時間
  ///
  /// [year] 目標年份
  /// [latitude] 緯度（用於生成星盤）
  /// [longitude] 經度（用於生成星盤）
  /// 返回四季節氣的時間列表
  Future<List<SeasonData>> calculateSeasonTimes(
    int year, {
    double latitude = 25.0, // 預設台北緯度
    double longitude = 121.0, // 預設台北經度
  }) async {
    logger.i('計算 $year 年的四季節氣時間');

    final List<SeasonData> seasons = [];

    for (final seasonType in SeasonType.values) {
      try {
        final dateTime = await _calculateSeasonDateTime(
          year,
          seasonType,
          latitude,
          longitude,
        );

        seasons.add(SeasonData(
          seasonType: seasonType,
          dateTime: dateTime,
        ));

        logger.d('${seasonType.displayName}: $dateTime');
      } catch (e) {
        logger.e('計算${seasonType.displayName}時間失敗: $e');
        // 使用近似時間作為備用
        final approximateDate = _getApproximateSeasonDate(year, seasonType);
        seasons.add(SeasonData(
          seasonType: seasonType,
          dateTime: approximateDate,
        ));
      }
    }

    return seasons;
  }

  /// 計算特定季節的精確時間
  Future<DateTime> _calculateSeasonDateTime(
    int year,
    SeasonType seasonType,
    double latitude,
    double longitude,
  ) async {
    logger.d('計算${seasonType.displayName}的精確時間');

    // 獲取近似日期作為搜索起點
    final approximateDate = _getApproximateSeasonDate(year, seasonType);

    // 設定搜索範圍（前後各5天）
    final searchStart = approximateDate.subtract(const Duration(days: 2));
    final searchEnd = approximateDate.add(const Duration(days: 2));

    logger.d('搜索範圍: $searchStart 到 $searchEnd');

    // 使用二分法尋找精確時間
    DateTime start = searchStart;
    DateTime end = searchEnd;
    DateTime mid = approximateDate;

    const double precision = 0.0001; // 精度為0.01度
    const int maxIterations = 100;
    int iterations = 0;

    while (iterations < maxIterations) {
      iterations++;

      // 計算中間時間
      final midMilliseconds =
          (start.millisecondsSinceEpoch + end.millisecondsSinceEpoch) ~/ 2;
      mid = DateTime.fromMillisecondsSinceEpoch(midMilliseconds);

      // 計算該時間的太陽經度
      final sunLongitude = await _calculateSunLongitude(mid, latitude, longitude);

      // 計算與目標經度的差距
      double diff = _calculateLongitudeDifference(sunLongitude, seasonType.solarLongitude);

      logger.d('第$iterations次迭代: $mid, 太陽經度: $sunLongitude, 差距: $diff');

      // 如果精度足夠，返回結果
      if (diff.abs() < precision) {
        logger.i('找到${seasonType.displayName}精確時間: $mid');
        return mid;
      }

      // 決定搜索方向
      if (diff > 0) {
        end = mid;
      } else {
        start = mid;
      }

      // 防止無限循環
      if (end.difference(start).inMilliseconds < 100) {
        break;
      }
    }

    logger.w('達到最大迭代次數，返回近似時間: $mid');
    return mid;
  }

  /// 計算指定時間的太陽經度
  Future<double> _calculateSunLongitude(
    DateTime dateTime,
    double latitude,
    double longitude,
  ) async {
    try {
      // 轉換為儒略日
      final julianDay = await JulianDateUtils.dateTimeToJulianDay(
        dateTime,
        latitude,
        longitude,
      );

      // 計算太陽位置
      final result = Sweph.swe_calc_ut(
        julianDay,
        HeavenlyBody.SE_SUN,
        SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_SPEED,
      );

      return result.longitude;
    } catch (e) {
      logger.e('計算太陽經度失敗: $e');
      throw Exception('計算太陽經度失敗: $e');
    }
  }

  /// 計算經度差距（考慮360度循環）
  double _calculateLongitudeDifference(double current, double target) {
    double diff = current - target;

    // 處理跨越0度的情況
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }

    return diff;
  }

  /// 獲取季節的近似日期
  DateTime _getApproximateSeasonDate(int year, SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return DateTime(year, 3, 20, 12, 0); // 春分約在3月20日
      case SeasonType.summerSolstice:
        return DateTime(year, 6, 21, 12, 0); // 夏至約在6月21日
      case SeasonType.autumnEquinox:
        return DateTime(year, 9, 23, 12, 0); // 秋分約在9月23日
      case SeasonType.winterSolstice:
        return DateTime(year, 12, 22, 12, 0); // 冬至約在12月22日
    }
  }

  /// 為季節節氣生成星盤數據
  Future<List<SeasonData>> generateSeasonCharts(
    List<SeasonData> seasons,
    BirthData location, {
    BirthData? natalPerson, // 用於比較的本命盤人物
  }) async {
    logger.i('為季節節氣生成星盤數據');

    final List<SeasonData> seasonsWithCharts = [];

    for (final season in seasons) {
      try {
        // 創建季節節氣的出生數據
        final seasonBirthData = BirthData(
          id: 'season_${season.seasonType.name}_${season.dateTime.year}',
          name: '${season.dateTime.year}年${season.seasonType.displayName}',
          birthDate: season.dateTime,
          latitude: location.latitude,
          longitude: location.longitude,
          birthPlace: location.birthPlace,
        );

        // 創建星盤數據
        final chartData = ChartData(
          chartType: ChartType.equinoxSolstice,
          primaryPerson: seasonBirthData,
          secondaryPerson: natalPerson, // 如果有本命盤人物，用於比較
          specificDate: season.dateTime,
        );

        seasonsWithCharts.add(SeasonData(
          seasonType: season.seasonType,
          dateTime: season.dateTime,
          chartData: chartData,
        ));

        logger.d('生成${season.seasonType.displayName}星盤數據完成');
      } catch (e) {
        logger.e('生成${season.seasonType.displayName}星盤數據失敗: $e');
        // 保留原始數據，不包含星盤
        seasonsWithCharts.add(season);
      }
    }

    return seasonsWithCharts;
  }

  /// 獲取當前年份的季節節氣
  Future<List<SeasonData>> getCurrentYearSeasons({
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
    BirthData? location,
  }) async {
    final currentYear = DateTime.now().year;
    final effectiveLocation = location ?? BirthData(
      id: 'default_location',
      name: '預設地點',
      birthDate: DateTime.now(),
      latitude: latitude,
      longitude: longitude,
      birthPlace: '台北',
    );

    final seasons = await calculateSeasonTimes(
      currentYear,
      latitude: effectiveLocation.latitude,
      longitude: effectiveLocation.longitude,
    );

    return await generateSeasonCharts(
      seasons,
      effectiveLocation,
      natalPerson: natalPerson,
    );
  }

  /// 獲取下一個即將到來的季節節氣
  Future<SeasonData?> getNextSeason({
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
    BirthData? location,
  }) async {
    final now = DateTime.now();
    final currentYear = now.year;

    // 獲取當前年份的季節
    final currentYearSeasons = await getCurrentYearSeasons(
      latitude: latitude,
      longitude: longitude,
      natalPerson: natalPerson,
      location: location,
    );

    // 尋找下一個季節
    for (final season in currentYearSeasons) {
      if (season.dateTime.isAfter(now)) {
        return season;
      }
    }

    // 如果當前年份沒有未來的季節，獲取下一年的第一個季節
    final nextYearSeasons = await calculateSeasonTimes(
      currentYear + 1,
      latitude: latitude,
      longitude: longitude,
    );

    if (nextYearSeasons.isNotEmpty) {
      final effectiveLocation = location ?? BirthData(
        id: 'default_location',
        name: '預設地點',
        birthDate: DateTime.now(),
        latitude: latitude,
        longitude: longitude,
        birthPlace: '台北',
      );

      final seasonsWithCharts = await generateSeasonCharts(
        [nextYearSeasons.first],
        effectiveLocation,
        natalPerson: natalPerson,
      );

      return seasonsWithCharts.first;
    }

    return null;
  }
}
