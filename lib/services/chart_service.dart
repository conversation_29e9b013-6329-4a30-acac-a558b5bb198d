import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/models/planet_position.dart';
import 'package:astreal/services/astrology_service.dart';
import 'package:astreal/utils/logger_utils.dart';

/// Service class for chart-related operations
class ChartService {
  final AstrologyService _astrologyService = AstrologyService();

  /// 計算行星之間的互容接納關係
  List<AspectInfo> calculateReceptions(List<PlanetPosition> planets) {
    return _astrologyService.calculateReceptions(planets);
  }

  /// Calculate planet positions for a chart
  ///
  /// 參數：
  /// - chartData: 星盤數據
  /// - planetVisibility: 行星可見性設定，從 ChartSettings 中取得
  /// - aspectOrbs: 相位容許度設定，從 ChartSettings 中取得
  Future<ChartData> calculatePlanetPositionsForChart(
    ChartData chartData, {
    Map<String, bool>? planetVisibility,
    Map<String, double>? aspectOrbs,
  }) async {
    try {
      switch (chartData.chartType) {
        case ChartType.natal:
        case ChartType.synastry:
        case ChartType.composite:
        case ChartType.davison:
        case ChartType.lunarReturn:
        case ChartType.solarReturn:
          return await _astrologyService.calculateChartData(
            chartData,
            planetVisibility: planetVisibility,
            aspectOrbs: aspectOrbs,
          );
        case ChartType.solarArcDirection:
        case ChartType.secondaryProgression:
        case ChartType.tertiaryProgression:
        case ChartType.transit:
          return await _astrologyService.calculateChartData(
            chartData,
            planetVisibility: planetVisibility,
            aspectOrbs: aspectOrbs,
          );

        default:
          return await _astrologyService.calculateChartData(
            chartData,
            planetVisibility: planetVisibility,
            aspectOrbs: aspectOrbs,
          );
      }
    } catch (e) {
      logger.e('Error calculating chart: $e');
      throw Exception('Failed to calculate chart: $e');
    }
  }
}
