import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../models/divination_record.dart';
import '../utils/logger_utils.dart';

/// 卜卦記錄服務
class DivinationRecordService {
  static const String _localStorageKey = 'divination_records';

  /// 保存卜卦記錄到本地存儲
  static Future<void> saveRecord(DivinationRecord record) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 獲取現有記錄
      final List<String> recordsJson = prefs.getStringList(_localStorageKey) ?? [];
      logger.d('現有記錄數量: ${recordsJson.length}');

      // 將新記錄添加到列表中
      final recordJsonString = jsonEncode(record.toJson());
      logger.d('正在保存記錄: ${record.id}, 問題: ${record.question}');
      recordsJson.add(recordJsonString);

      // 保存更新後的列表
      await prefs.setStringList(_localStorageKey, recordsJson);
      logger.d('卜卦記錄已保存: ${record.id}, 總記錄數: ${recordsJson.length}');
    } catch (e) {
      logger.e('保存卜卦記錄時出錯: $e');
      throw Exception('保存卜卦記錄時出錯: $e');
    }
  }

  /// 獲取所有卜卦記錄
  static Future<List<DivinationRecord>> getAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 獲取記錄 JSON 字符串列表
      final List<String> recordsJson = prefs.getStringList(_localStorageKey) ?? [];
      logger.d('從本地存儲獲取到 ${recordsJson.length} 條記錄');

      if (recordsJson.isEmpty) {
        logger.d('本地存儲中無卜卦記錄');
        return [];
      }

      // 將 JSON 字符串轉換為記錄對象
      final records = <DivinationRecord>[];
      for (int i = 0; i < recordsJson.length; i++) {
        try {
          final Map<String, dynamic> data = jsonDecode(recordsJson[i]);
          final record = DivinationRecord.fromJson(data);
          records.add(record);
          logger.d('成功解析記錄 ${i + 1}: ${record.id}, 問題: ${record.question}');
        } catch (e) {
          logger.e('解析第 ${i + 1} 條記錄時出錯: $e');
        }
      }

      // 按時間戳降序排序（最新的在前面）
      records.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      logger.d('最終返回 ${records.length} 條記錄');
      return records;
    } catch (e) {
      logger.e('獲取卜卦記錄時出錯: $e');
      return [];
    }
  }

  /// 刪除卜卦記錄
  static Future<void> deleteRecord(String recordId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 獲取現有記錄
      final List<String> recordsJson = prefs.getStringList(_localStorageKey) ?? [];

      // 過濾掉要刪除的記錄
      final filteredRecords = recordsJson.where((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return data['id'] != recordId;
      }).toList();

      // 保存更新後的列表
      await prefs.setStringList(_localStorageKey, filteredRecords);

      logger.d('卜卦記錄已刪除: $recordId');
    } catch (e) {
      logger.e('刪除卜卦記錄時出錯: $e');
      throw Exception('刪除卜卦記錄時出錯: $e');
    }
  }

  /// 清除所有卜卦記錄
  static Future<void> clearAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localStorageKey);
      logger.d('所有卜卦記錄已清除');
    } catch (e) {
      logger.e('清除卜卦記錄時出錯: $e');
      throw Exception('清除卜卦記錄時出錯: $e');
    }
  }
}
