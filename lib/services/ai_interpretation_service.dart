import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/chart_data.dart';
import '../utils/logger_utils.dart';

class AIInterpretationService {
  static const String _selectedModelKey = 'selected_ai_model';

  // API 端點映射
  static const Map<String, String> _apiEndpoints = {
    'mixtral-8x7b-32768': 'https://api.groq.com/openai/v1/chat/completions',
    'claude-3-opus-20240229': 'https://api.anthropic.com/v1/messages',
    'gpt-4-0125-preview': 'https://api.openai.com/v1/chat/completions',
    'chatgpt-4o-latest': 'https://api.openai.com/v1/chat/completions',
    'gpt-4.5-preview': 'https://api.openai.com/v1/chat/completions',
    'gemma2-9b-it': 'https://api.groq.com/openai/v1/chat/completions',
    'llama-3.3-70b-versatile':
        'https://api.groq.com/openai/v1/chat/completions',
  };

  // API Key 環境變量映射
  static const Map<String, String> _apiKeyEnvNames = {
    'mixtral-8x7b-32768':
        '********************************************************',
    'claude-3-opus-20240229':
        '********************************************************',
    'chatgpt-4o-latest':
        '********************************************************************************************************************************************************************',
    'gemma2-9b-it': '********************************************************',
    'llama-3.3-70b-versatile':
        '********************************************************',
  };

  static Future<String> getSelectedModel() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedModelKey) ?? 'gemma2-9b-it';
  }

  static Future<String> getInterpretation(
    ChartData chartData,
    String prompt,
  ) async {
    try {
      final startTime = DateTime.now();
      final selectedModel = await getSelectedModel();
      final apiEndpoint = _apiEndpoints[selectedModel];
      final apiKeyEnvName = _apiKeyEnvNames[selectedModel];

      logger.i("====== AI 解讀請求開始 ======");
      logger.i("選擇的模型：$selectedModel");
      logger.i("API 端點：$apiEndpoint");
      logger.i("請求時間：${startTime.toIso8601String()}");
      logger.d("星盤數據：\n${jsonEncode(chartData.toJson())}");
      logger.d("提示詞：\n$prompt");

      if (apiEndpoint == null || apiKeyEnvName == null) {
        logger.e("不支援的 AI 模型：$selectedModel");
        throw Exception('不支援的 AI 模型');
      }

      final apiKey = apiKeyEnvName;
      if (apiKey.isEmpty) {
        logger.e("API Key 未設置");
        throw Exception('未設置 API Key');
      }

      final requestBody = {
        'model': selectedModel,
        'messages': [
          {
            'role': 'system',
            'content': '你是一個專業的占星師',
          },
          {
            'role': 'user',
            'content': prompt,
          },
        ],
        // 'temperature': 0.7,
      };

      logger.d("請求標頭：\n${jsonEncode({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $apiKey',
            //'Accept': 'application/json',
          })}");
      logger.d("請求內容：\n${jsonEncode(requestBody)}");

      final response = await http.post(
        Uri.parse(apiEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
          // 'Accept': 'application/json',
        },
        body: utf8.encode(jsonEncode(requestBody)),
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i("請求耗時：${duration.inMilliseconds}ms");
      logger.i("響應狀態碼：${response.statusCode}");
      logger.d("響應標頭：\n${jsonEncode(response.headers)}");

      if (response.statusCode == 200) {
        final responseBody = utf8.decode(response.bodyBytes);
        final jsonResponse = jsonDecode(responseBody);
        logger.d("原始響應內容：\n$responseBody");

        final content =
            jsonResponse['choices'][0]['message']['content'] as String;
        final decodedContent = utf8.decode(utf8.encode(content));

        logger.i("====== AI 解讀請求完成 ======");
        logger.d("處理後的響應內容：\n$decodedContent");

        return decodedContent;
      } else {
        final errorBody = utf8.decode(response.bodyBytes);
        logger.e("API 請求失敗（${response.statusCode}）：$errorBody");
        logger.i("====== AI 解讀請求失敗 ======");
        throw Exception('API 請求失敗（${response.statusCode}）：$errorBody');
      }
    } catch (e, stackTrace) {
      logger.e("AI 解讀錯誤：$e");
      logger.e("錯誤堆疊：$stackTrace");
      logger.i("====== AI 解讀請求異常 ======");
      throw Exception('獲取 AI 解讀時發生錯誤：$e');
    }
  }

  static Future<String> getDualChartInterpretation(
    // ChartInterpretationModel person1Data,
    // ChartInterpretationModel person2Data,
    String prompt,
  ) async {
    try {
      final startTime = DateTime.now();
      final selectedModel = await getSelectedModel();
      final apiEndpoint = _apiEndpoints[selectedModel];
      final apiKeyEnvName = _apiKeyEnvNames[selectedModel];

      logger.i("====== 雙圈盤 AI 解讀請求開始 ======");
      logger.i("選擇的模型：$selectedModel");
      logger.i("API 端點：$apiEndpoint");
      logger.i("請求時間：${startTime.toIso8601String()}");
      // logger.d("第一位：\n${jsonEncode(person1Data.toJson())}");
      // logger.d("第二位：\n${jsonEncode(person2Data.toJson())}");
      logger.d("提示詞：\n$prompt");

      if (apiEndpoint == null || apiKeyEnvName == null) {
        logger.e("不支援的 AI 模型：$selectedModel");
        throw Exception('不支援的 AI 模型');
      }

      final apiKey = apiKeyEnvName;
      if (apiKey.isEmpty) {
        logger.e("API Key 未設置");
        throw Exception('未設置 API Key');
      }

      final requestBody = {
        'model': selectedModel,
        'messages': [
          {
            'role': 'system',
            'content': '你是一位專業的占星師',
          },
          {
            'role': 'user',
            'content': prompt,
          },
        ],
        // 'temperature': 0.7,
      };

      logger.d("請求標頭：\n${jsonEncode({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $apiKey',
            'Accept': 'application/json',
          })}");
      logger.d("請求內容：\n${jsonEncode(requestBody)}");

      final response = await http.post(
        Uri.parse(apiEndpoint),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Bearer $apiKey',
          'Accept': 'application/json',
        },
        body: utf8.encode(jsonEncode({
          'model': selectedModel,
          'messages': [
            {
              'role': 'system',
              'content': '你是一位專業的占星師\n',
            },
            {
              'role': 'user',
              'content': prompt,
            },
          ],
          'temperature': 0.0,
          // 'max_tokens': 2000,
        })),
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      logger.i("請求耗時：${duration.inMilliseconds}ms");
      logger.i("響應狀態碼：${response.statusCode}");
      logger.d("響應標頭：\n${jsonEncode(response.headers)}");

      if (response.statusCode == 200) {
        final responseBody = utf8.decode(response.bodyBytes);
        final jsonResponse = jsonDecode(responseBody);
        logger.d("原始響應內容：\n$responseBody");

        final content =
            jsonResponse['choices'][0]['message']['content'] as String;
        final decodedContent = utf8.decode(utf8.encode(content));

        logger.i("====== 雙圈盤 AI 解讀請求完成 ======");
        logger.d("處理後的響應內容：\n$decodedContent");

        return decodedContent;
      } else {
        final errorBody = utf8.decode(response.bodyBytes);
        logger.e("API 請求失敗（${response.statusCode}）：$errorBody");
        logger.i("====== 雙圈盤 AI 解讀請求失敗 ======");
        throw Exception('API 請求失敗（${response.statusCode}）：$errorBody');
      }
    } catch (e, stackTrace) {
      logger.e("雙圈盤 AI 解讀錯誤：$e");
      logger.e("錯誤堆疊：$stackTrace");
      logger.i("====== 雙圈盤 AI 解讀請求異常 ======");
      throw Exception('獲取雙圈盤 AI 解讀時發生錯誤：$e');
    }
  }
}
