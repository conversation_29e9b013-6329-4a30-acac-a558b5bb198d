import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';

import '../models/booking_model.dart';
import '../utils/LoggerUtils.dart';

/// 電子郵件服務類，用於發送電子郵件通知
class EmailService {
  // 開發模式標誌，在開發模式下模擬發送電子郵件
  static const bool isDevelopmentMode = true;

  // 占星師的電子郵件地址
  static const String astrologerEmail = '<EMAIL>';

  // 發送郵件的郵箱配置
  static const String senderEmail = '<EMAIL>';
  static const String senderPassword = 'your_app_password'; // 使用應用專用密碼
  static const String senderName = 'Astreal 占星預約系統';

  /// 發送預約通知郵件給占星師
  static Future<bool> sendBookingNotification(BookingModel booking) async {
    try {
      // 在開發模式下模擬發送電子郵件
      if (isDevelopmentMode) {
        // 創建郵件內容
        final emailHtml = _buildEmailHtml(booking);

        // 記錄郵件內容
        logger.i('模擬發送郵件到: $astrologerEmail');
        logger.i('主旨: 新的占星諮詢預約: ${booking.name}');
        logger.i('郵件內容:\n$emailHtml');

        // 模擬發送確認郵件給客戶
        _sendConfirmationEmail(booking);

        return true;
      } else {
        // 創建 SMTP 伺服器
        final smtpServer = gmail(senderEmail, senderPassword);

        // 創建郵件
        final message = Message()
          ..from = Address(senderEmail, senderName)
          ..recipients.add(astrologerEmail)
          ..subject = '新的占星諮詢預約: ${booking.name}'
          ..html = _buildEmailHtml(booking);

        // 發送郵件
        final sendReport = await send(message, smtpServer);
        logger.i('郵件發送成功: ${sendReport.toString()}');

        // 發送確認郵件給客戶
        await _sendConfirmationEmail(booking);

        return true;
      }
    } catch (e) {
      logger.e('發送郵件失敗: $e');
      return false;
    }
  }

  /// 發送確認郵件給客戶
  static Future<bool> _sendConfirmationEmail(BookingModel booking) async {
    try {
      // 在開發模式下模擬發送電子郵件
      if (isDevelopmentMode) {
        // 創建郵件內容
        final emailHtml = _buildConfirmationEmailHtml(booking);

        // 記錄郵件內容
        logger.i('模擬發送確認郵件到: ${booking.email}');
        logger.i('主旨: 您的占星諮詢預約確認');
        logger.i('郵件內容:\n$emailHtml');

        return true;
      } else {
        // 創建 SMTP 伺服器
        final smtpServer = gmail(senderEmail, senderPassword);

        // 創建郵件
        final message = Message()
          ..from = Address(senderEmail, senderName)
          ..recipients.add(booking.email)
          ..subject = '您的占星諮詢預約確認'
          ..html = _buildConfirmationEmailHtml(booking);

        // 發送郵件
        final sendReport = await send(message, smtpServer);
        logger.i('確認郵件發送成功: ${sendReport.toString()}');

        return true;
      }
    } catch (e) {
      logger.e('發送確認郵件失敗: $e');
      return false;
    }
  }

  /// 構建預約通知郵件的 HTML 內容
  static String _buildEmailHtml(BookingModel booking) {
    final birthData = booking.birthData;
    final birthDataHtml = birthData != null
        ? '''
          <h3>客戶星盤資料</h3>
          <p><strong>姓名:</strong> ${birthData['name']}</p>
          <p><strong>出生日期時間:</strong> ${birthData['birthDate']}</p>
          <p><strong>出生地點:</strong> ${birthData['birthPlace']}</p>
          <p><strong>經度:</strong> ${birthData['longitude']}</p>
          <p><strong>緯度:</strong> ${birthData['latitude']}</p>
          ${birthData['notes'] != null ? '<p><strong>備註:</strong> ${birthData['notes']}</p>' : ''}
        '''
        : '<p>客戶未提供星盤資料</p>';

    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          h2 { color: #6200ea; }
          h3 { color: #7c4dff; margin-top: 20px; }
          .info-block { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .footer { margin-top: 30px; font-size: 12px; color: #777; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>新的占星諮詢預約</h2>

          <div class="info-block">
            <h3>預約資訊</h3>
            <p><strong>預約 ID:</strong> ${booking.id}</p>
            <p><strong>客戶姓名:</strong> ${booking.name}</p>
            <p><strong>電子郵件:</strong> ${booking.email}</p>
            <p><strong>電話:</strong> ${booking.phone}</p>
            <p><strong>預約日期:</strong> ${booking.bookingDate.year}-${booking.bookingDate.month.toString().padLeft(2, '0')}-${booking.bookingDate.day.toString().padLeft(2, '0')}</p>
            <p><strong>預約時間:</strong> ${booking.bookingTime.hour.toString().padLeft(2, '0')}:${booking.bookingTime.minute.toString().padLeft(2, '0')}</p>
            <p><strong>諮詢類型:</strong> ${booking.consultationType}</p>
            ${booking.notes != null ? '<p><strong>備註:</strong> ${booking.notes}</p>' : ''}
          </div>

          <div class="info-block">
            $birthDataHtml
          </div>

          <div class="footer">
            <p>此郵件由 Astreal 占星預約系統自動發送。</p>
          </div>
        </div>
      </body>
      </html>
    ''';
  }

  /// 構建確認郵件的 HTML 內容
  static String _buildConfirmationEmailHtml(BookingModel booking) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          h2 { color: #6200ea; }
          h3 { color: #7c4dff; margin-top: 20px; }
          .info-block { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .footer { margin-top: 30px; font-size: 12px; color: #777; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>您的占星諮詢預約已確認</h2>

          <p>親愛的 ${booking.name}，</p>

          <p>感謝您預約我們的占星諮詢服務。以下是您的預約詳情：</p>

          <div class="info-block">
            <h3>預約資訊</h3>
            <p><strong>預約 ID:</strong> ${booking.id}</p>
            <p><strong>預約日期:</strong> ${booking.bookingDate.year}-${booking.bookingDate.month.toString().padLeft(2, '0')}-${booking.bookingDate.day.toString().padLeft(2, '0')}</p>
            <p><strong>預約時間:</strong> ${booking.bookingTime.hour.toString().padLeft(2, '0')}:${booking.bookingTime.minute.toString().padLeft(2, '0')}</p>
            <p><strong>諮詢類型:</strong> ${booking.consultationType}</p>
          </div>

          <p>我們的占星師將會在預約時間與您聯繫。如需更改或取消預約，請回覆此郵件或聯繫我們。</p>

          <div class="footer">
            <p>此郵件由 Astreal 占星預約系統自動發送。</p>
          </div>
        </div>
      </body>
      </html>
    ''';
  }
}
