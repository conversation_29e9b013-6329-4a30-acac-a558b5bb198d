import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';

import '../models/planet_position.dart';
import '../widgets/arabic_points_widget.dart';

/// 古典占星資料顯示組件
///
/// 顯示行星的尊貴力量（廟旺落陷）和互容接納關係
class ClassicalAstrologyWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const ClassicalAstrologyWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  /// 根據行星尊貴力量狀態返回對應的顏色
  Color _getDignityColor(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return Colors.purple; // 廟狀態用紫色
      case PlanetDignity.exaltation:
        return Colors.green; // 旺狀態用綠色
      case PlanetDignity.detriment:
        return Colors.red; // 陷狀態用紅色
      case PlanetDignity.fall:
        return Colors.orange; // 弱狀態用橙色
      case PlanetDignity.normal:
      default:
        return Colors.grey; // 普通狀態用灰色
    }
  }

  /// 根據互容接納類型返回對應的顏色
  Color _getReceptionColor(ReceptionType receptionType) {
    switch (receptionType) {
      case ReceptionType.mutualReception:
        return Colors.purple; // 互相互容用紫色
      case ReceptionType.reception:
        return Colors.teal; // 互容用藍綠色
      case ReceptionType.mutualAcceptance:
        return Colors.deepPurple; // 互相接納用深紫色
      case ReceptionType.acceptance:
        return Colors.indigo; // 接納用靛藍色
      case ReceptionType.none:
      default:
        return Colors.grey; // 無接納用灰色
    }
  }

  @override
  Widget build(BuildContext context) {
    // 獲取行星位置列表
    final planets = viewModel.chartData.planets;

    // 獲取互容接納關係列表
    final allReceptions = viewModel.chartData.aspects?.where(
      (aspect) => aspect.receptionType != ReceptionType.none
    ).toList() ?? [];

    // 過濾掉重複的互容接納關係
    final receptions = _filterDuplicateReceptions(allReceptions);

    if (planets == null || planets.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('沒有行星數據',
                style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          TabBar(
            labelColor: Theme.of(context).primaryColor,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: '行星尊貴力量'),
              Tab(text: '互容接納'),
              Tab(text: '特殊點'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                // 行星尊貴力量標籤頁
                _buildDignityTab(planets),

                // 互容接納關係標籤頁
                _buildReceptionTab(receptions),

                // 特殊點（阿拉伯點）標籤頁
                ArabicPointsWidget(viewModel: viewModel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建行星尊貴力量標籤頁
  Widget _buildDignityTab(List<dynamic> planets) {
    return ListView.builder(
      itemCount: planets.length,
      itemBuilder: (context, index) {
        final planet = planets[index];

        // 跳過特殊點位（如阿拉伯點）
        if (planet.id >= 100) {
          return const SizedBox.shrink();
        }

        return Card(
          color: Colors.white,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: _getDignityColor(planet.dignity).withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 行星符號圓形圖標
                CircleAvatar(
                  backgroundColor: planet.color,
                  radius: 24,
                  child: Text(
                    planet.symbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontFamily: "astro_one_font",
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${planet.name} (${planet.sign})',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.auto_awesome,
                            size: 16,
                            color: _getDignityColor(planet.dignity),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '尊貴力量: ${planet.getDignityText()}',
                            style: TextStyle(
                              fontSize: 16,
                              color: _getDignityColor(planet.dignity),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getDignityDescription(planet),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建互容接納關係標籤頁
  Widget _buildReceptionTab(List<AspectInfo> receptions) {
    if (receptions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('沒有互容接納關係',
                style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: receptions.length,
      itemBuilder: (context, index) {
        final reception = receptions[index];
        final receptionColor = _getReceptionColor(reception.receptionType);

        return Card(
          color: Colors.white,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: receptionColor.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 互容接納符號圓形圖標
                CircleAvatar(
                  backgroundColor: receptionColor,
                  radius: 24,
                  child: const Icon(
                    Icons.swap_horiz,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${reception.planet1.name} ↔ ${reception.planet2.name}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.swap_horiz,
                            size: 16,
                            color: receptionColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            reception.getReceptionText(),
                            style: TextStyle(
                              fontSize: 16,
                              color: receptionColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        reception.receptionDescription ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildPlanetDetails(reception.planet1),
                      const SizedBox(height: 4),
                      _buildPlanetDetails(reception.planet2),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建行星詳細信息
  Widget _buildPlanetDetails(dynamic planet) {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: planet.color.withOpacity(0.2),
          radius: 12,
          child: Text(
            planet.symbol,
            style: TextStyle(
              color: planet.color,
              fontSize: 14,
              fontFamily: "astro_one_font",
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${planet.name}: ${planet.sign}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  /// 過濾掉重複的互容接納關係
  ///
  /// 當兩個行星之間存在互容接納關係時，可能會有兩條記錄：
  /// 1. 行星A與行星B的接納關係
  /// 2. 行星B與行星A的接納關係
  ///
  /// 這個方法會過濾掉重複的記錄，只保留一條
  List<AspectInfo> _filterDuplicateReceptions(List<AspectInfo> receptions) {
    final filteredReceptions = <AspectInfo>[];
    final processedPairs = <String>{};

    for (final reception in receptions) {
      // 創建行星對的唯一標識符（按ID排序以確保一致性）
      final planetIds = [reception.planet1.id, reception.planet2.id];
      planetIds.sort(); // 排序以確保 [A,B] 和 [B,A] 產生相同的標識符
      final pairKey = planetIds.join('-');

      // 如果這對行星還沒有處理過，則添加到結果中
      if (!processedPairs.contains(pairKey)) {
        filteredReceptions.add(reception);
        processedPairs.add(pairKey);
      }
    }

    return filteredReceptions;
  }

  /// 獲取行星尊貴力量的描述
  String _getDignityDescription(dynamic planet) {
    switch (planet.dignity) {
      case PlanetDignity.domicile:
        return '${planet.name}在${planet.sign}為廟，表示其力量最強，能充分發揮其特質。';
      case PlanetDignity.exaltation:
        return '${planet.name}在${planet.sign}為旺，表示其力量增強，能良好發揮其特質。';
      case PlanetDignity.detriment:
        return '${planet.name}在${planet.sign}為陷，表示其力量受阻，難以發揮其特質。';
      case PlanetDignity.fall:
        return '${planet.name}在${planet.sign}為弱，表示其力量減弱，不易發揮其特質。';
      case PlanetDignity.normal:
      default:
        return '${planet.name}在${planet.sign}為普通狀態，力量適中。';
    }
  }
}
