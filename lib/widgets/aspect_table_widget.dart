import 'package:astreal/models/aspect_info.dart' show ReceptionType, AspectDirection;
import 'package:astreal/utils/astrology_calculator.dart' as astro;
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';

class AspectTableWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const AspectTableWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  /// 根據互容接納類型返回對應的顏色
  Color _getReceptionColor(ReceptionType receptionType) {
    switch (receptionType) {
      case ReceptionType.mutualReception:
        return Colors.purple; // 互相互容用紫色
      case ReceptionType.reception:
        return Colors.teal; // 互容用藍綠色
      case ReceptionType.mutualAcceptance:
        return Colors.deepPurple; // 互相接納用深紫色
      case ReceptionType.acceptance:
        return Colors.indigo; // 接納用靛藍色
      case ReceptionType.none:
      default:
        return Colors.grey; // 無接納用灰色
    }
  }

  @override
  Widget build(BuildContext context) {
    return viewModel.chartData.aspects!.isEmpty
        ? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.info_outline, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text('沒有相位',
                    style: TextStyle(fontSize: 18, color: Colors.grey)),
              ],
            ),
          )
        : ListView.builder(
            itemCount: viewModel.chartData.aspects!.length,
            itemBuilder: (context, index) {
              final aspect = viewModel.chartData.aspects![index];
              final aspectColor = astro.AstrologyCalculator.getAspectColor(
                aspect.aspect,
              );

              return Card(
                color: Colors.white,
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 3),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: aspectColor.withOpacity(0.3), width: 1),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      // 相位符號圓形圖標（縮小）
                      CircleAvatar(
                        backgroundColor: aspectColor,
                        radius: 16,
                        child: Text(
                          aspect.symbol,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontFamily: "astro_one_font",
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // 相位名稱
                      Text(
                        '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // 角度信息
                      Text(
                        '(${aspect.orb.toStringAsFixed(1)}°)',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      // 標籤區域（緊湊排列）
                      Wrap(
                        spacing: 4,
                        children: [
                          // 入相/出相標籤
                          if (aspect.direction != null)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: (aspect.direction == AspectDirection.applying ? Colors.green : Colors.orange).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: (aspect.direction == AspectDirection.applying ? Colors.green : Colors.orange).withOpacity(0.3),
                                  width: 0.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    aspect.direction == AspectDirection.applying ? Icons.arrow_forward : Icons.arrow_back,
                                    size: 12,
                                    color: aspect.direction == AspectDirection.applying ? Colors.green : Colors.orange,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    aspect.getDirectionText(),
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: aspect.direction == AspectDirection.applying ? Colors.green : Colors.orange,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          // 互容接納標籤
                          if (aspect.receptionType != ReceptionType.none)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getReceptionColor(aspect.receptionType).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: _getReceptionColor(aspect.receptionType).withOpacity(0.3),
                                  width: 0.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.swap_horiz,
                                    size: 12,
                                    color: _getReceptionColor(aspect.receptionType),
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    aspect.getReceptionText(),
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: _getReceptionColor(aspect.receptionType),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }
}
