import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../models/birth_data.dart';
import '../ui/AppTheme.dart';
import '../utils/LoggerUtils.dart';
import '../viewmodels/chart_viewmodel.dart';

class TimeAdjustmentWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const TimeAdjustmentWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<TimeAdjustmentWidget> createState() => _TimeAdjustmentWidgetState();
}

class _TimeAdjustmentWidgetState extends State<TimeAdjustmentWidget> {
  // 調整單位選項
  final List<String> _adjustmentUnits = ['5分鐘', '30分鐘', '1小時', '1天', '1個月', '1年'];
  String _selectedUnit = '5分鐘';

  // 記錄原始日期和調整後的日期
  late DateTime _originalDate;
  late DateTime _adjustedDate;

  // 記錄調整的總量
  int _totalAdjustment = 0;

  @override
  void initState() {
    super.initState();
    _resetAdjustment();
  }

  @override
  void didUpdateWidget(TimeAdjustmentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果星盤數據變更，重置調整
    // 注意：我們一定要重置調整，因為在切換星盤時，即使屬性相同，也應該重置
    bool shouldReset = false;

    // 檢查星盤數據是否變更
    if (oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      print('星盤數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查行星數據是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartData.planets != widget.viewModel.chartData.planets) {
      print('行星數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查宮位數據是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartData.houses != widget.viewModel.chartData.houses) {
      print('宮位數據變更，重置調整');
      shouldReset = true;
    }

    // 檢查其他屬性是否變更
    if (!shouldReset && (
        oldWidget.viewModel.chartType != widget.viewModel.chartType ||
        oldWidget.viewModel.primaryPerson != widget.viewModel.primaryPerson ||
        oldWidget.viewModel.secondaryPerson != widget.viewModel.secondaryPerson ||
        oldWidget.viewModel.specificDate != widget.viewModel.specificDate)) {
      print('其他屬性變更，重置調整');
      shouldReset = true;
    }

    // 檢查星盤更新計數器是否變更
    if (!shouldReset &&
        oldWidget.viewModel.chartUpdateCounter != widget.viewModel.chartUpdateCounter) {
      print('星盤更新計數器變更，重置調整: ${oldWidget.viewModel.chartUpdateCounter} -> ${widget.viewModel.chartUpdateCounter}');
      shouldReset = true;
    }
  }

  // 重置調整
  void _resetAdjustment() {
    // 根據星盤類型決定要調整的日期
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      // 推運盤或返照盤：調整特定日期
      _originalDate = widget.viewModel.specificDate ?? DateTime.now();
    } else {
      // 一般星盤：調整主要人物的出生日期
      _originalDate = widget.viewModel.primaryPerson.birthDate;
    }
    _adjustedDate = _originalDate;
    _totalAdjustment = 0;
  }

  // 重置為預設時間
  void _resetToDefaultTime() {
    // 根據星盤類型決定要重置的日期
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      // 推運盤或返照盤：重置為當前時間
      final defaultDate = DateTime.now();

      setState(() {
        _adjustedDate = defaultDate;
        _originalDate = defaultDate;
        _totalAdjustment = 0;
      });

      // 更新星盤
      widget.viewModel.setSpecificDate(defaultDate);
      logger.d('重置為預設時間: 當前時間 $defaultDate');
    } else {
      // 一般星盤：重置為原始出生日期（不調整）
      final originalBirthData = widget.viewModel.primaryPerson;

      setState(() {
        _adjustedDate = originalBirthData.birthDate;
        _originalDate = originalBirthData.birthDate;
        _totalAdjustment = 0;
      });

      // 更新星盤
      final updatedPerson = BirthData(
        id: originalBirthData.id,
        name: originalBirthData.name,
        birthDate: originalBirthData.birthDate,
        birthPlace: originalBirthData.birthPlace,
        latitude: originalBirthData.latitude,
        longitude: originalBirthData.longitude,
        notes: originalBirthData.notes,
        createdAt: originalBirthData.createdAt,
      );
      widget.viewModel.setPrimaryPerson(updatedPerson);
      logger.d('重置為預設時間: 原始出生日期 ${originalBirthData.birthDate}');
    }

    // 重要！更新星盤
    _updateChart();
  }

  // 調整時間
  void _adjustTime(bool increase) {
    setState(() {
      Duration adjustment;
      int minutesAdjustment = 0;

      // 根據選擇的單位確定調整量
      switch (_selectedUnit) {
        case '5分鐘':
          adjustment = const Duration(minutes: 5);
          minutesAdjustment = 5;
          break;
        case '30分鐘':
          adjustment = const Duration(minutes: 30);
          minutesAdjustment = 30;
          break;
        case '1小時':
          adjustment = const Duration(hours: 1);
          minutesAdjustment = 60;
          break;
        case '1天':
          adjustment = const Duration(days: 1);
          minutesAdjustment = 1440; // 1天 = 1440分鐘
          break;
        case '1個月':
          // 月份調整需要特殊處理
          final newDate = DateTime(
            _adjustedDate.year,
            _adjustedDate.month + (increase ? 1 : -1),
            _adjustedDate.day,
            _adjustedDate.hour,
            _adjustedDate.minute,
          );
          _adjustedDate = newDate;
          _totalAdjustment += increase ? 43200 : -43200; // 約30天 = 43200分鐘
          logger.d('調整時間: 月份${increase ? '增加' : '減少'}, 結果: $_adjustedDate');
          _updateChart();
          return;
        case '1年':
          // 年份調整需要特殊處理
          final newDate = DateTime(
            _adjustedDate.year + (increase ? 1 : -1),
            _adjustedDate.month,
            _adjustedDate.day,
            _adjustedDate.hour,
            _adjustedDate.minute,
          );
          _adjustedDate = newDate;
          _totalAdjustment += increase ? 525600 : -525600; // 約365天 = 525600分鐘
          logger.d('調整時間: 年份${increase ? '增加' : '減少'}, 結果: $_adjustedDate');
          _updateChart();
          return;
        default:
          adjustment = const Duration(minutes: 5);
          minutesAdjustment = 5;
      }

      // 應用調整
      _adjustedDate = increase
          ? _adjustedDate.add(adjustment)
          : _adjustedDate.subtract(adjustment);

      // 更新總調整量
      _totalAdjustment += increase ? minutesAdjustment : -minutesAdjustment;

      logger.d('調整時間: $_selectedUnit ${increase ? '增加' : '減少'}, 結果: $_adjustedDate');

      // 更新星盤
      _updateChart();
    });
  }

  // 更新星盤
  void _updateChart() {
    // 根據星盤類型決定要更新的日期
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      // 推運盤或返照盤：更新特定日期
      widget.viewModel.setSpecificDate(_adjustedDate);
    } else {
      // 一般星盤：更新主要人物的出生日期
      final updatedPerson = BirthData(
        id: widget.viewModel.primaryPerson.id,
        name: widget.viewModel.primaryPerson.name,
        birthDate: _adjustedDate,
        birthPlace: widget.viewModel.primaryPerson.birthPlace,
        latitude: widget.viewModel.primaryPerson.latitude,
        longitude: widget.viewModel.primaryPerson.longitude,
      );
      widget.viewModel.setPrimaryPerson(updatedPerson);
    }
  }

  // 格式化調整量顯示
  String _formatAdjustment() {
    if (_totalAdjustment == 0) return '';

    final sign = _totalAdjustment > 0 ? '+' : '';

    if (_totalAdjustment.abs() < 60) {
      // 小於1小時，顯示分鐘
      return ' ($sign$_totalAdjustment分鐘)';
    } else if (_totalAdjustment.abs() < 1440) {
      // 小於1天，顯示小時
      final hours = _totalAdjustment / 60;
      return ' ($sign${hours.toStringAsFixed(1)}小時)';
    } else if (_totalAdjustment.abs() < 43200) {
      // 小於30天，顯示天
      final days = _totalAdjustment / 1440;
      return ' ($sign${days.toStringAsFixed(1)}天)';
    } else if (_totalAdjustment.abs() < 525600) {
      // 小於365天，顯示月
      final months = _totalAdjustment / 43200;
      return ' ($sign${months.toStringAsFixed(1)}個月)';
    } else {
      // 大於等於365天，顯示年
      final years = _totalAdjustment / 525600;
      return ' ($sign${years.toStringAsFixed(1)}年)';
    }
  }

  @override
  Widget build(BuildContext context) {
    // 確定要顯示的日期文本
    String dateText;
    if (widget.viewModel.chartType.isPredictiveChart ||
        widget.viewModel.chartType.isReturnChart) {
      dateText = '${_formatDateTime(_adjustedDate)}${_formatAdjustment()}';
    } else {
      dateText = '${_formatDateTime(_adjustedDate)}${_formatAdjustment()}';
    }

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      elevation: 2,
      shadowColor: AppColors.royalIndigo.withOpacity(0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 減少按鈕 (左側)
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade50,
                  foregroundColor: Colors.red.shade700,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(color: Colors.red.shade200),
                  ),
                ),
                onPressed: () => _adjustTime(false),
                child: const Icon(Icons.remove, size: 14),
              ),
            ),
            const SizedBox(width: 4),

            // 時間顯示 - 可點擊選擇
            Expanded(
              child: GestureDetector(
                onTap: _showDateTimePicker,
                child: Container(
                  height: 28,
                  padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.royalIndigo.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Text(
                          dateText,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 2),
                      Icon(
                        Icons.calendar_today,
                        size: 12,
                        color: AppColors.royalIndigo.withOpacity(0.7),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),

            // 調整單位選擇
            SizedBox(
              height: 28,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 0),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedUnit,
                    isDense: true,
                    icon: const Icon(Icons.arrow_drop_down, color: AppColors.royalIndigo, size: 14),
                    style: const TextStyle(
                      color: AppColors.textDark,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedUnit = newValue;
                        });
                      }
                    },
                    items: _adjustmentUnits
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, style: const TextStyle(fontSize: 10)),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),

            // 增加按鈕 (右側)
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade50,
                  foregroundColor: Colors.green.shade700,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(color: Colors.green.shade200),
                  ),
                ),
                onPressed: () => _adjustTime(true),
                child: const Icon(Icons.add, size: 14),
              ),
            ),
            const SizedBox(width: 4),

            // 重置按鈕
            SizedBox(
              width: 28,
              height: 28,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo.withOpacity(0.1),
                  foregroundColor: AppColors.royalIndigo,
                  elevation: 0,
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side: BorderSide(color: AppColors.royalIndigo.withOpacity(0.2)),
                  ),
                ),
                onPressed: _resetToDefaultTime,
                child: const Icon(Icons.restore, size: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示日期時間選擇器
  void _showDateTimePicker() {
    // 創建一個臨時日期變量，用於在用戶確認前存儲選擇
    DateTime tempDate = _adjustedDate;

    // 顯示底部彈出的日期時間選擇器
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: 320,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              // 標題和按鈕
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消', style: TextStyle(color: Colors.grey)),
                  ),
                  const Text(
                    '選擇日期和時間',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _adjustedDate = tempDate;
                        _totalAdjustment = _calculateTotalAdjustment(_originalDate, _adjustedDate);
                      });
                      _updateChart();
                      Navigator.pop(context);
                    },
                    child: const Text('確定', style: TextStyle(color: AppColors.royalIndigo)),
                  ),
                ],
              ),
              const Divider(),
              // 日期時間選擇器
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.dateAndTime,
                  initialDateTime: _adjustedDate,
                  minimumDate: DateTime(1900),
                  maximumDate: DateTime(2100),
                  onDateTimeChanged: (DateTime newDateTime) {
                    tempDate = newDateTime;
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 計算總調整量（分鐘）
  int _calculateTotalAdjustment(DateTime original, DateTime adjusted) {
    return adjusted.difference(original).inMinutes;
  }

  // 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
