import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../models/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import '../models/chart_type.dart';
import '../utils/zodiac_symbols.dart';

/// 基礎星盤繪製類
abstract class BaseChartPainter extends CustomPainter {
  final List<AspectInfo> aspects;
  final HouseCuspData housesData;
  final ChartType chartType;
  double deltaAngle = 0;

  // 定義各個圓環的半徑比例常數
  static const double OUTER_CIRCLE_RADIUS_RATIO = 1.0; // 最外圈
  static const double ZODIAC_CIRCLE_RADIUS_RATIO = 0.9; // 星座符號圈
  static const double INNER_CIRCLE_RADIUS_RATIO = 0.8; // 內圈
  static const double HOUSE_NUMBER_RADIUS_RATIO = 0.75; // 宮位數字圈
  static const double ASPECT_LINE_RADIUS_RATIO = 0.4; // 相位線圈

  BaseChartPainter({
    required this.aspects,
    required this.housesData,
    required this.chartType,
  });

  // 獲取實際使用的上升點度數
  double get _effectiveAscendantDegree => housesData.ascmc[0];

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    deltaAngle = 180 - housesData.cusps[1];

    // 繪製基本圓圈
    drawCircles(canvas, center, radius);

    // 繪製星座分隔線
    _drawZodiacLines(canvas, center, radius);

    // 繪製宮位分隔線
    _drawHouseLines(canvas, center, radius);

    // 繪製宮位數字
    _drawHouseNumbers(canvas, center, radius);

    // 繪製星座符號
    _drawZodiacSymbols(canvas, center, radius);

    // 繪製相位線
    // _drawAspectLines(canvas, center, radius);

    // 繪製行星符號
    drawPlanetSymbols(canvas, center, radius);

    // 繪製上升點標記
    _drawAscendantMarker(canvas, center, radius);
  }

  // 由子類實現的方法
  void drawPlanetSymbols(Canvas canvas, Offset center, double radius);

  // 繪製基本圓圈和背景
  void drawCircles(Canvas canvas, Offset center, double radius) {
    // 繪製漸變背景
    const bgGradient = RadialGradient(
      colors: [
        Colors.white,
        const Color(0xFFF8F8FF),
      ],
      stops: const [0.7, 1.0],
    );

    final bgPaint = Paint()
      ..shader = bgGradient.createShader(
        Rect.fromCircle(center: center, radius: radius),
      );

    canvas.drawCircle(center, radius, bgPaint);

    // 添加陰影效果
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    canvas.drawCircle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, shadowPaint);

    // 繪製外圈
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    canvas.drawCircle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, paint);

    // 繪製內圈
    paint.strokeWidth = 1.0;
    paint.color = Colors.black.withOpacity(0.5);
    canvas.drawCircle(center, radius * INNER_CIRCLE_RADIUS_RATIO, paint);
  }

  // 共用的繪製方法
  void _drawZodiacLines(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0);
      final outerPoint =
          getPointByAngle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, angle);
      final innerPoint =
          getPointByAngle(center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, angle);

      // 使用虛線效果
      final dashPath = Path();
      dashPath.moveTo(outerPoint.dx, outerPoint.dy);
      dashPath.lineTo(innerPoint.dx, innerPoint.dy);

      // 繪製虛線
      canvas.drawPath(
        dashPath,
        paint,
      );
    }
  }

  void _drawHouseLines(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8;

    for (int i = 1; i <= 12; i++) {
      final houseAngle = housesData.cusps[i];
      final outerPoint = getPointByAngle(
          center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, houseAngle);
      final innerPoint = getPointByAngle(
          center, radius * INNER_CIRCLE_RADIUS_RATIO, houseAngle);

      // 使用漸變色彩
      final gradient = LinearGradient(
        colors: [
          Colors.blue.withOpacity(0.3),
          Colors.purple.withOpacity(0.3),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );

      final gradientPaint = Paint()
        ..shader = gradient.createShader(
          Rect.fromPoints(outerPoint, innerPoint),
        )
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.8;

      canvas.drawLine(outerPoint, innerPoint, gradientPaint);
    }
  }

  void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
    const textStyle = TextStyle(
      color: Colors.black,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );

    for (int i = 1; i <= 12; i++) {
      final houseAngle = housesData.cusps[i];
      final nextHouseAngle = housesData.cusps[i % 12 + 1];

      // 計算宮位中點角度
      double midAngle;
      if (nextHouseAngle < houseAngle) {
        midAngle = (houseAngle + (nextHouseAngle + 360)) / 2;
        if (midAngle >= 360) midAngle -= 360;
      } else {
        midAngle = (houseAngle + nextHouseAngle) / 2;
      }

      final position =
          getPointByAngle(center, radius * HOUSE_NUMBER_RADIUS_RATIO, midAngle);
      final textPainter = TextPainter(
        text: TextSpan(
          text: '$i',
          style: textStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        position.translate(-textPainter.width / 2, -textPainter.height / 2),
      );
    }
  }

  void _drawZodiacSymbols(Canvas canvas, Offset center, double radius) {
    final zodiacPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0 + 15.0);
      final position =
          getPointByAngle(center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, angle);

      final zodiacTextStyle = TextStyle(
          color: ZodiacSymbols.getZodiacColor(signs[i]),
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFeatures: const [FontFeature.disable('liga')],
          fontFamily: 'astro_one_font');

      zodiacPainter.text = TextSpan(
        text: ZodiacSymbols.ZODIAC_SYMBOLS[signs[i]] ?? '?',
        style: zodiacTextStyle,
      );
      zodiacPainter.layout();
      zodiacPainter.paint(
        canvas,
        position.translate(-zodiacPainter.width / 2, -zodiacPainter.height / 2),
      );
    }
  }

  void _drawAscendantMarker(Canvas canvas, Offset center, double radius) {
    final angle = (_effectiveAscendantDegree);
    final position =
        getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);

    final path = Path();
    path.moveTo(position.dx, position.dy);
    path.lineTo(position.dx + 10, position.dy + 5);
    path.lineTo(position.dx + 10, position.dy - 5);
    path.close();

    canvas.drawPath(
      path,
      Paint()
        ..color = Colors.red
        ..style = PaintingStyle.fill,
    );
  }

  // 由子類實現的方法，用於獲取相位角度
  double getAspectAngle1(AspectInfo aspect);
  double getAspectAngle2(AspectInfo aspect);

  // 根據角度計算點的位置
  Offset getPointByAngle(Offset pt, double r, double angle) {
    double angleTemp = angle + deltaAngle;
    double x = pt.dx + r * cos(angleTemp * pi / 180);
    double y = pt.dy - r * sin(angleTemp * pi / 180);
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
