import 'package:flutter/material.dart';

import '../constants/astrology_constants.dart';
import '../models/planet_position.dart';
import '../ui/AppTheme.dart';
import '../viewmodels/chart_viewmodel.dart';

/// 星盤元素統計小部件
/// 顯示星盤中的元素分佈情況，包括火土風水、啟動/固定/變動、陰性/陽性
class ChartElementsWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  ChartElementsWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  // 保存當前的 BuildContext
  BuildContext? _context;

  @override
  Widget build(BuildContext context) {
    // 保存 context 以便在其他方法中使用
    _context = context;

    if (viewModel.isLoading || viewModel.chartData.planets == null || viewModel.chartData.planets!.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    // 計算元素分佈
    final elementStats = _calculateElementStats(viewModel.chartData.planets!);
    final modalityStats = _calculateModalityStats(viewModel.chartData.planets!);
    final polarityStats = _calculatePolarityStats(viewModel.chartData.planets!);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('星盤元素統計'),
          const SizedBox(height: 16),

          // 元素分佈（火土風水）
          _buildElementsSection(elementStats),
          const SizedBox(height: 24),

          // 三大性質（啟動、固定、變動）
          _buildModalitiesSection(modalityStats),
          const SizedBox(height: 24),

          // 陰陽分佈
          _buildPolaritySection(polarityStats),

          const SizedBox(height: 24),

          // 行星星座詳細信息
          _buildPlanetSignDetails(viewModel.chartData.planets!),

          const SizedBox(height: 24),

          // 元素解釋
          _buildElementsExplanation(elementStats),
        ],
      ),
    );
  }

  /// 計算元素分佈（火土風水）
  Map<String, int> _calculateElementStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '火': 0,
      '土': 0,
      '風': 0,
      '水': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        final element = _getElementFromSign(planet.sign);
        if (element != null) {
          stats[element] = (stats[element] ?? 0) + 1;
        }
      }
    }

    return stats;
  }

  /// 計算品質分佈（啟動、固定、變動）
  Map<String, int> _calculateModalityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '啟動': 0,
      '固定': 0,
      '變動': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        final modality = _getModalityFromSign(planet.sign);
        if (modality != null) {
          stats[modality] = (stats[modality] ?? 0) + 1;
        }
      }
    }

    return stats;
  }

  /// 計算陰陽分佈
  Map<String, int> _calculatePolarityStats(List<PlanetPosition> planets) {
    final Map<String, int> stats = {
      '陽性': 0,
      '陰性': 0,
    };

    for (final planet in planets) {
      // 只計算主要行星（太陽到冥王星）
      if (planet.id >= 0 && planet.id <= 9) {
        final polarity = AstrologyConstants.SIGN_POLARITY[planet.sign] ?? false;
        final polarityKey = polarity ? '陽性' : '陰性';
        stats[polarityKey] = (stats[polarityKey] ?? 0) + 1;
      }
    }

    return stats;
  }

  /// 根據星座獲取元素
  String? _getElementFromSign(String sign) {
    switch (sign) {
      case '牡羊座':
      case '獅子座':
      case '射手座':
        return '火';
      case '金牛座':
      case '處女座':
      case '摩羯座':
        return '土';
      case '雙子座':
      case '天秤座':
      case '水瓶座':
        return '風';
      case '巨蟹座':
      case '天蠍座':
      case '雙魚座':
        return '水';
      default:
        return null;
    }
  }

  /// 根據星座獲取品質
  String? _getModalityFromSign(String sign) {
    switch (sign) {
      case '牡羊座':
      case '巨蟹座':
      case '天秤座':
      case '摩羯座':
        return '啟動';
      case '金牛座':
      case '獅子座':
      case '天蠍座':
      case '水瓶座':
        return '固定';
      case '雙子座':
      case '處女座':
      case '射手座':
      case '雙魚座':
        return '變動';
      default:
        return null;
    }
  }

  /// 構建元素分佈部分
  Widget _buildElementsSection(Map<String, int> stats) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '四大元素',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildElementItem('火', stats['火'] ?? 0, Colors.red),
                _buildElementItem('土', stats['土'] ?? 0, Colors.brown),
                _buildElementItem('風', stats['風'] ?? 0, Colors.lightBlue),
                _buildElementItem('水', stats['水'] ?? 0, Colors.blue),
              ],
            ),
            const SizedBox(height: 16),
            _buildElementsBarChart(stats),
          ],
        ),
      ),
    );
  }

  /// 構建品質分佈部分
  Widget _buildModalitiesSection(Map<String, int> stats) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '三大性質',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildElementItem('啟動', stats['啟動'] ?? 0, Colors.orange),
                _buildElementItem('固定', stats['固定'] ?? 0, Colors.green),
                _buildElementItem('變動', stats['變動'] ?? 0, Colors.purple),
              ],
            ),
            const SizedBox(height: 16),
            _buildModalitiesBarChart(stats),
          ],
        ),
      ),
    );
  }

  /// 構建陰陽分佈部分
  Widget _buildPolaritySection(Map<String, int> stats) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '陰陽分佈',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildElementItem('陽性', stats['陽性'] ?? 0, Colors.amber),
                _buildElementItem('陰性', stats['陰性'] ?? 0, Colors.indigo),
              ],
            ),
            const SizedBox(height: 16),
            _buildPolarityBarChart(stats),
          ],
        ),
      ),
    );
  }

  /// 構建元素項目
  Widget _buildElementItem(String name, int count, Color color) {
    return InkWell(
      onTap: () {
        // 點擊後顯示詳細信息
        _showElementDetailsDialog(name, color);
      },
      borderRadius: BorderRadius.circular(30),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
              border: Border.all(color: color, width: 2),
            ),
            child: Center(
              child: Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            name,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建元素條形圖
  Widget _buildElementsBarChart(Map<String, int> stats) {
    final int total = stats.values.fold(0, (sum, count) => sum + count);
    if (total == 0) return const SizedBox.shrink();

    return Column(
      children: [
        _buildBarChartItem('火', stats['火'] ?? 0, total, Colors.red),
        const SizedBox(height: 8),
        _buildBarChartItem('土', stats['土'] ?? 0, total, Colors.brown),
        const SizedBox(height: 8),
        _buildBarChartItem('風', stats['風'] ?? 0, total, Colors.lightBlue),
        const SizedBox(height: 8),
        _buildBarChartItem('水', stats['水'] ?? 0, total, Colors.blue),
      ],
    );
  }

  /// 構建品質條形圖
  Widget _buildModalitiesBarChart(Map<String, int> stats) {
    final int total = stats.values.fold(0, (sum, count) => sum + count);
    if (total == 0) return const SizedBox.shrink();

    return Column(
      children: [
        _buildBarChartItem('啟動', stats['啟動'] ?? 0, total, Colors.orange),
        const SizedBox(height: 8),
        _buildBarChartItem('固定', stats['固定'] ?? 0, total, Colors.green),
        const SizedBox(height: 8),
        _buildBarChartItem('變動', stats['變動'] ?? 0, total, Colors.purple),
      ],
    );
  }

  /// 構建陰陽條形圖
  Widget _buildPolarityBarChart(Map<String, int> stats) {
    final int total = stats.values.fold(0, (sum, count) => sum + count);
    if (total == 0) return const SizedBox.shrink();

    return Column(
      children: [
        _buildBarChartItem('陽性', stats['陽性'] ?? 0, total, Colors.amber),
        const SizedBox(height: 8),
        _buildBarChartItem('陰性', stats['陰性'] ?? 0, total, Colors.indigo),
      ],
    );
  }

  /// 構建條形圖項目
  Widget _buildBarChartItem(String name, int count, int total, Color color) {
    final double percentage = total > 0 ? count / total : 0;

    return InkWell(
      onTap: () {
        // 點擊後顯示詳細信息
        _showElementDetailsDialog(name, color);
      },
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            SizedBox(
              width: 50,
              child: Text(
                name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Stack(
                children: [
                  Container(
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  FractionallySizedBox(
                    widthFactor: percentage,
                    child: Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 50,
              child: Text(
                '${(percentage * 100).toStringAsFixed(0)}%',
                textAlign: TextAlign.right,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示元素詳細信息對話框
  void _showElementDetailsDialog(String elementName, Color color) {
    // 確保有可用的 context
    if (_context == null) return;

    // 根據元素名稱確定元素類型
    String elementType = '';
    List<PlanetPosition> elementPlanets = [];

    if (['火', '土', '風', '水'].contains(elementName)) {
      elementType = '元素';
      for (final planet in viewModel.chartData.planets!) {
        if (planet.id >= 0 && planet.id <= 9) {
          final element = _getElementFromSign(planet.sign);
          if (element == elementName) {
            elementPlanets.add(planet);
          }
        }
      }
    } else if (['啟動', '固定', '變動'].contains(elementName)) {
      elementType = '品質';
      for (final planet in viewModel.chartData.planets!) {
        if (planet.id >= 0 && planet.id <= 9) {
          final modality = _getModalityFromSign(planet.sign);
          if (modality == elementName) {
            elementPlanets.add(planet);
          }
        }
      }
    } else if (['陽性', '陰性'].contains(elementName)) {
      elementType = '陰陽性';
      for (final planet in viewModel.chartData.planets!) {
        if (planet.id >= 0 && planet.id <= 9) {
          final polarity = AstrologyConstants.SIGN_POLARITY[planet.sign] ?? false;
          final polarityKey = polarity ? '陽性' : '陰性';
          if (polarityKey == elementName) {
            elementPlanets.add(planet);
          }
        }
      }
    }

    // 顯示對話框
    showDialog(
      context: _context!,
      builder: (context) => AlertDialog(
        title: Text(
          '$elementName$elementType詳細',
          style: TextStyle(color: color),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '包含以下行星：',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              ...elementPlanets.map((planet) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: color.withOpacity(0.2),
                      child: Text(
                        planet.symbol,
                        style: TextStyle(
                          color: color,
                          fontFamily: "astro_one_font",
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            planet.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text('星座：${planet.sign}'),
                          Text('宮位：${planet.getHouseText()}'),
                        ],
                      ),
                    ),
                  ],
                ),
              )).toList(),
              if (elementPlanets.isEmpty)
                const Text('無行星'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 構建元素解釋
  Widget _buildElementsExplanation(Map<String, int> stats) {
    // 找出最多和最少的元素
    String? dominantElement;
    String? weakestElement;
    int maxCount = -1;
    int minCount = 999;

    stats.forEach((element, count) {
      if (count > maxCount) {
        maxCount = count;
        dominantElement = element;
      }
      if (count < minCount) {
        minCount = count;
        weakestElement = element;
      }
    });

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '元素解釋',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '主導元素：${dominantElement ?? "無"}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(_getElementExplanation(dominantElement)),
            const SizedBox(height: 16),
            Text(
              '最弱元素：${weakestElement ?? "無"}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(_getWeakElementExplanation(weakestElement)),
            const SizedBox(height: 16),
            const Text(
              '元素含義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '火：熱情、活力、創造力、行動力\n'
              '土：穩定、實際、耐心、可靠\n'
              '風：思考、溝通、社交、適應力\n'
              '水：情感、直覺、敏感、同理心',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              '品質含義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '啟動：主動、開創、領導力\n'
              '固定：穩定、堅持、耐力\n'
              '變動：適應、靈活、變通',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 獲取元素解釋
  String _getElementExplanation(String? element) {
    switch (element) {
      case '火':
        return '您的星盤中火元素較為突出，表示您可能充滿熱情、活力和創造力。您喜歡行動和冒險，有強烈的自我表達慾望，並且能夠激勵他人。';
      case '土':
        return '您的星盤中土元素較為突出，表示您可能非常務實、穩定和可靠。您重視安全感和物質基礎，有很強的耐心和毅力，能夠腳踏實地地實現目標。';
      case '風':
        return '您的星盤中風元素較為突出，表示您可能思維活躍、善於溝通和社交。您喜歡新想法和新體驗，適應力強，能夠從多角度看待問題。';
      case '水':
        return '您的星盤中水元素較為突出，表示您可能情感豐富、直覺敏銳和富有同理心。您對他人的感受敏感，有很強的想像力和創造力，能夠深入理解人性。';
      default:
        return '您的星盤中各元素分佈較為平衡，表示您可能在不同情況下能夠靈活運用不同的特質和能力。';
    }
  }

  /// 獲取弱元素解釋
  String _getWeakElementExplanation(String? element) {
    switch (element) {
      case '火':
        return '您的星盤中火元素較弱，可能需要更多地培養熱情、自信和行動力。嘗試參與更多能激發您熱情的活動，學習更主動地表達自己。';
      case '土':
        return '您的星盤中土元素較弱，可能需要更多地培養穩定性、耐心和實際性。建立更有規律的生活習慣，學習更踏實地處理日常事務。';
      case '風':
        return '您的星盤中風元素較弱，可能需要更多地培養思考能力、溝通技巧和社交能力。嘗試接觸更多新思想，參與更多社交活動。';
      case '水':
        return '您的星盤中水元素較弱，可能需要更多地培養情感表達、直覺和同理心。嘗試更多地關注自己和他人的情感需求，發展創造性和藝術性的一面。';
      default:
        return '您的星盤中各元素分佈較為平衡，不存在明顯的弱勢元素。';
    }
  }

  /// 構建行星星座詳細信息
  Widget _buildPlanetSignDetails(List<PlanetPosition> planets) {
    // 按元素分組行星
    final Map<String, List<PlanetPosition>> elementPlanets = {
      '火': [],
      '土': [],
      '風': [],
      '水': [],
    };

    // 按性質分組行星
    final Map<String, List<PlanetPosition>> modalityPlanets = {
      '啟動': [],
      '固定': [],
      '變動': [],
    };

    // 按陰陽性分組行星
    final Map<String, List<PlanetPosition>> polarityPlanets = {
      '陽性': [],
      '陰性': [],
    };

    // 只考慮主要行星（太陽到冥王星）
    for (final planet in planets) {
      if (planet.id >= 0 && planet.id <= 9) {
        // 元素分組
        final element = _getElementFromSign(planet.sign);
        if (element != null) {
          elementPlanets[element]!.add(planet);
        }

        // 性質分組
        final modality = _getModalityFromSign(planet.sign);
        if (modality != null) {
          modalityPlanets[modality]!.add(planet);
        }

        // 陰陽性分組
        final polarity = AstrologyConstants.SIGN_POLARITY[planet.sign] ?? false;
        final polarityKey = polarity ? '陽性' : '陰性';
        polarityPlanets[polarityKey]!.add(planet);
      }
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '行星星座詳細',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 元素分組詳細
            const Text('元素分組：', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildElementPlanetsList('火', elementPlanets['火']!, Colors.red),
            _buildElementPlanetsList('土', elementPlanets['土']!, Colors.brown),
            _buildElementPlanetsList('風', elementPlanets['風']!, Colors.lightBlue),
            _buildElementPlanetsList('水', elementPlanets['水']!, Colors.blue),
            const SizedBox(height: 16),

            // 性質分組詳細
            const Text('性質分組：', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildElementPlanetsList('啟動', modalityPlanets['啟動']!, Colors.orange),
            _buildElementPlanetsList('固定', modalityPlanets['固定']!, Colors.green),
            _buildElementPlanetsList('變動', modalityPlanets['變動']!, Colors.purple),
            const SizedBox(height: 16),

            // 陰陽性分組詳細
            const Text('陰陽性分組：', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildElementPlanetsList('陽性', polarityPlanets['陽性']!, Colors.amber),
            _buildElementPlanetsList('陰性', polarityPlanets['陰性']!, Colors.indigo),
          ],
        ),
      ),
    );
  }

  /// 構建元素行星列表
  Widget _buildElementPlanetsList(String elementName, List<PlanetPosition> planets, Color color) {
    if (planets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.only(left: 8, bottom: 8),
        child: Row(
          children: [
            Text(
              '$elementName: ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const Text('無行星'),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$elementName: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: planets.map((planet) {
                return Chip(
                  backgroundColor: color.withOpacity(0.1),
                  side: BorderSide(color: color),
                  label: Text(
                    '${planet.name}(${planet.sign})',
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  avatar: CircleAvatar(
                    backgroundColor: color,
                    child: Text(
                      planet.symbol,
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: "astro_one_font",
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建標題
  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        const Icon(Icons.auto_awesome, color: AppColors.royalIndigo),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
      ],
    );
  }
}
