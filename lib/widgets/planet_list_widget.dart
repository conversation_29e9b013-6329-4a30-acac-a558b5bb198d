import 'package:astreal/models/planet_position.dart';
import 'package:astreal/utils/astrology_calculator.dart' as astro;
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:flutter/material.dart';

class PlanetListWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const PlanetListWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  /// 根據行星尊貴力量狀態返回對應的顏色
  Color _getDignityColor(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return Colors.purple; // 廟狀態用紫色
      case PlanetDignity.exaltation:
        return Colors.green; // 旺狀態用綠色
      case PlanetDignity.detriment:
        return Colors.red; // 陷狀態用紅色
      case PlanetDignity.fall:
        return Colors.orange; // 弱狀態用橙色
      case PlanetDignity.normal:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: viewModel.chartData.planets!.length,
      itemBuilder: (context, index) {
        final planet = viewModel.chartData.planets![index];
        final signDegree = planet.longitude % 30;
        final planetColor = astro.AstrologyCalculator.getPlanetColor(
          planet.name,
        );

        return Card(
          color: Colors.white,
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 3),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: planetColor.withOpacity(0.3), width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                // 行星符號圓形圖標（縮小）
                CircleAvatar(
                  backgroundColor: planetColor,
                  radius: 16,
                  child: Text(
                    planet.symbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: "astro_one_font",
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 行星名稱
                Text(
                  planet.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                // 位置信息
                Text(
                  '${planet.sign} ${viewModel.formatDegree(signDegree)}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                // 標籤區域（緊湊排列）
                Wrap(
                  spacing: 4,
                  children: [
                    // 宮位標籤
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.indigo.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.indigo.withOpacity(0.3),
                          width: 0.5,
                        ),
                      ),
                      child: Text(
                        '第${planet.house}宮',
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: Colors.indigo,
                        ),
                      ),
                    ),
                    // 尊貴力量標籤
                    if (planet.dignity != PlanetDignity.normal)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getDignityColor(planet.dignity).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: _getDignityColor(planet.dignity).withOpacity(0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          planet.getDignityText(),
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: _getDignityColor(planet.dignity),
                          ),
                        ),
                      ),
                    // 逆行標籤
                    if (planet.longitudeSpeed < 0)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.red.withOpacity(0.3),
                            width: 0.5,
                          ),
                        ),
                        child: const Text(
                          '逆行',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: Colors.red,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
