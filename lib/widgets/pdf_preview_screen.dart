import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// PDF 預覽螢幕
/// 用於顯示生成的 PDF 檔案
class PdfPreviewScreen extends StatefulWidget {
  final Uint8List pdfBytes;
  final String title;

  const PdfPreviewScreen({
    super.key,
    required this.pdfBytes,
    required this.title,
  });

  @override
  State<PdfPreviewScreen> createState() => _PdfPreviewScreenState();
}

class _PdfPreviewScreenState extends State<PdfPreviewScreen> {
  String? _pdfPath;
  bool _isLoading = true;
  String? _errorMessage;
  int _totalPages = 0;
  int _currentPage = 0;
  bool _swipeHorizontal = true;
  PDFViewController? _pdfViewController;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 將 PDF 字節數據保存到臨時文件
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/preview_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await tempFile.writeAsBytes(widget.pdfBytes);

      setState(() {
        _pdfPath = tempFile.path;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '載入 PDF 時發生錯誤: $e';
        _isLoading = false;
      });
      print('載入 PDF 時發生錯誤: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: Icon(_swipeHorizontal ? Icons.swap_horiz : Icons.swap_vert),
            onPressed: () {
              setState(() {
                _swipeHorizontal = !_swipeHorizontal;
              });
            },
            tooltip: _swipeHorizontal ? '切換為垂直滾動' : '切換為水平滾動',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () async {
              if (_pdfPath != null) {
                try {
                  await Share.shareXFiles(
                    [XFile(_pdfPath!)],
                    text: widget.title,
                  );
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('分享失敗: $e')),
                    );
                  }
                }
              }
            },
            tooltip: '分享 PDF',
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _pdfPath != null && _totalPages > 0
          ? BottomAppBar(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('第 ${_currentPage + 1} 頁 / 共 $_totalPages 頁'),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.arrow_back),
                          onPressed: _currentPage > 0
                              ? () {
                                  _jumpToPage(_currentPage - 1);
                                }
                              : null,
                        ),
                        IconButton(
                          icon: const Icon(Icons.arrow_forward),
                          onPressed: _currentPage < _totalPages - 1
                              ? () {
                                  _jumpToPage(_currentPage + 1);
                                }
                              : null,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在載入 PDF，請稍候...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 50),
            const SizedBox(height: 16),
            Text(_errorMessage!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPdf,
              child: const Text('重試'),
            ),
          ],
        ),
      );
    }

    if (_pdfPath == null) {
      return const Center(
        child: Text('無法載入 PDF'),
      );
    }

    // 使用 flutter_pdfview 顯示 PDF
    return PDFView(
      filePath: _pdfPath!,
      enableSwipe: true,
      swipeHorizontal: _swipeHorizontal,
      autoSpacing: true,
      pageFling: true,
      pageSnap: true,
      defaultPage: _currentPage,
      fitPolicy: FitPolicy.BOTH,
      preventLinkNavigation: false,
      onRender: (pages) {
        setState(() {
          _totalPages = pages!;
        });
      },
      onError: (error) {
        setState(() {
          _errorMessage = error.toString();
        });
        print('PDF 渲染錯誤: $error');
      },
      onPageError: (page, error) {
        print('第 $page 頁發生錯誤: $error');
      },
      onViewCreated: (PDFViewController pdfViewController) {
        setState(() {
          _pdfViewController = pdfViewController;
        });
      },
      onPageChanged: (int? page, int? total) {
        if (page != null) {
          setState(() {
            _currentPage = page;
          });
        }
      },
    );
  }

  void _jumpToPage(int page) {
    if (_pdfViewController != null) {
      _pdfViewController!.setPage(page);
    }
    setState(() {
      _currentPage = page;
    });
  }

  @override
  void dispose() {
    // 清理臨時文件
    if (_pdfPath != null) {
      try {
        File(_pdfPath!).delete().then((_) {
          print('臨時 PDF 文件已刪除');
        });
      } catch (e) {
        print('刪除臨時 PDF 文件失敗: $e');
      }
    }
    super.dispose();
  }
}
