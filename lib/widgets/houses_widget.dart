import 'package:astreal/utils/zodiac_utils.dart' as zodiac;
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:astreal/widgets/custom_house_card.dart';
import 'package:flutter/material.dart';

import '../utils/astrology_calculator.dart';

class HousesWidget extends StatelessWidget {
  final ChartViewModel viewModel;

  const HousesWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  /// 根據宮頭經度獲取宮主星（包含傳統和現代宮主星）
  Map<String, List<String>> _getHouseRulers(double cuspLongitude) {
    // 獲取宮頭所在星座
    final signIndex = (cuspLongitude / 30).floor() % 12;

    // 根據星座確定宮主（傳統和現代）
    switch (signIndex) {
      case 0: return {'traditional': ['火星'], 'modern': ['火星']}; // 牡羊座
      case 1: return {'traditional': ['金星'], 'modern': ['金星']}; // 金牛座
      case 2: return {'traditional': ['水星'], 'modern': ['水星']}; // 雙子座
      case 3: return {'traditional': ['月亮'], 'modern': ['月亮']}; // 巨蟹座
      case 4: return {'traditional': ['太陽'], 'modern': ['太陽']}; // 獅子座
      case 5: return {'traditional': ['水星'], 'modern': ['水星']}; // 處女座
      case 6: return {'traditional': ['金星'], 'modern': ['金星']}; // 天秤座
      case 7: return {'traditional': ['火星'], 'modern': ['火星', '冥王星']}; // 天蠍座
      case 8: return {'traditional': ['木星'], 'modern': ['木星']}; // 射手座
      case 9: return {'traditional': ['土星'], 'modern': ['土星']}; // 摩羯座
      case 10: return {'traditional': ['土星'], 'modern': ['土星', '天王星']}; // 水瓶座
      case 11: return {'traditional': ['木星'], 'modern': ['木星', '海王星']}; // 雙魚座
      default: return {'traditional': ['未知'], 'modern': ['未知']};
    }
  }

  /// 獲取宮主星的詳細信息（支持多個宮主星）
  List<Map<String, dynamic>> _getHouseRulerInfoList(int houseNumber) {
    if (viewModel.chartData.houses == null) {
      return [];
    }

    final cuspLongitude = viewModel.chartData.houses!.cusps[houseNumber];
    final rulers = _getHouseRulers(cuspLongitude);
    final planets = viewModel.chartData.planets ?? [];
    final List<Map<String, dynamic>> rulerInfoList = [];

    // 優先使用現代宮主星，如果沒有則使用傳統宮主星
    final modernRulers = rulers['modern'] ?? [];
    final traditionalRulers = rulers['traditional'] ?? [];
    final rulersToUse = modernRulers.isNotEmpty ? modernRulers : traditionalRulers;

    for (final rulerName in rulersToUse) {
      if (rulerName == '未知') continue;

      // 在行星列表中尋找宮主星
      final ruler = planets.where((p) => p.name == rulerName).toList();

      if (ruler.isNotEmpty) {
        rulerInfoList.add({
          'name': rulerName,
          'house': ruler.first.house,
          'sign': ruler.first.sign,
          'symbol': ruler.first.symbol,
          'color':AstrologyCalculator.getPlanetColor(rulerName),
          'isTraditional': traditionalRulers.contains(rulerName) && !modernRulers.contains(rulerName),
          'isModern': modernRulers.contains(rulerName) && modernRulers.length > 1,
        });
      }
    }

    return rulerInfoList;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 12,
      itemBuilder: (context, index) {
        final houseNumber = index + 1;
        final houseAngle =
            viewModel.chartData.houses!.cusps[houseNumber];
        final sign = viewModel.getZodiacSign(houseAngle);
        final signDegree = houseAngle % 30;
        final houseDescription = zodiac.ZodiacUtils.getHouseDescription(
          houseNumber,
        );
        final houseColor = viewModel.getHouseColor(houseNumber);

        // 獲取在此宮位的行星
        final planetsInHouse =
            viewModel.chartData.planets!.where((p) => p.house == houseNumber).toList();

        // 獲取宮主星信息列表
        final rulerInfoList = _getHouseRulerInfoList(houseNumber);

        return CustomHouseCard(
          houseNumber: houseNumber,
          houseAngle: houseAngle,
          sign: sign,
          signDegree: signDegree,
          houseDescription: houseDescription,
          houseColor: houseColor,
          rulerInfoList: rulerInfoList,
          planetsInHouse: planetsInHouse,
          viewModel: viewModel,
        );
      },
    );
  }
}
