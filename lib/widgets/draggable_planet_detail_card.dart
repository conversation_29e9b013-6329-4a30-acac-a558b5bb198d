import 'package:flutter/material.dart';

import '../models/aspect_info.dart';
import '../models/planet_position.dart';
import '../viewmodels/chart_viewmodel.dart';
import 'planet_detail_card.dart';

/// 可拖動的行星詳情卡片
class DraggablePlanetDetailCard extends StatefulWidget {
  final PlanetPosition planet;
  final List<AspectInfo> aspects;
  final List<AspectInfo> receptions;
  final ChartViewModel viewModel;
  final VoidCallback onClose;
  final Offset initialPosition;

  const DraggablePlanetDetailCard({
    Key? key,
    required this.planet,
    required this.aspects,
    required this.receptions,
    required this.viewModel,
    required this.onClose,
    required this.initialPosition,
  }) : super(key: key);

  @override
  State<DraggablePlanetDetailCard> createState() => _DraggablePlanetDetailCardState();
}

class _DraggablePlanetDetailCardState extends State<DraggablePlanetDetailCard> {
  late Offset _position;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
  }

  @override
  Widget build(BuildContext context) {
    // 確保卡片不會超出螢幕範圍
    final screenSize = MediaQuery.of(context).size;
    final cardWidth = 280.0; // 卡片寬度
    final cardHeight = screenSize.height * 0.7; // 卡片高度上限

    // 限制卡片位置在螢幕內
    double safeX = _position.dx;
    double safeY = _position.dy;

    // 確保卡片不會超出右邊界
    if (safeX + cardWidth > screenSize.width) {
      safeX = screenSize.width - cardWidth - 10;
    }

    // 確保卡片不會超出左邊界
    if (safeX < 10) {
      safeX = 10;
    }

    // 確保卡片不會超出下邊界
    if (safeY + cardHeight > screenSize.height) {
      safeY = screenSize.height - cardHeight - 10;
    }

    // 確保卡片不會超出上邊界
    if (safeY < 10) {
      safeY = 10;
    }

    return Positioned(
      left: safeX,
      top: safeY,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
          });
        },
        onPanUpdate: (details) {
          setState(() {
            _position = Offset(
              _position.dx + details.delta.dx,
              _position.dy + details.delta.dy,
            );
          });
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: AnimatedOpacity(
          opacity: _isDragging ? 0.3 : 1, // 拖動時更透明
          duration: const Duration(milliseconds: 200),
          child: Material(
            color: Colors.transparent,
            child: PlanetDetailCard(
              planet: widget.planet,
              aspects: widget.aspects,
              receptions: widget.receptions,
              viewModel: widget.viewModel,
              onClose: widget.onClose,
            ),
          ),
        ),
      ),
    );
  }
}
