import 'package:flutter/material.dart';

import '../ui/AppTheme.dart';

/// 統一風格的卡片組件
class StyledCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? color;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final BorderRadius? borderRadius;

  const StyledCard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.only(bottom: 16.0),
    this.elevation = 2.0,
    this.color,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final cardBorderRadius = borderRadius ?? BorderRadius.circular(16.0);

    return Card(
      elevation: elevation,
      margin: margin,
      shape: RoundedRectangleBorder(
        borderRadius: cardBorderRadius,
        side: BorderSide(
          color: isSelected ? AppColors.royalIndigo : Colors.transparent,
          width: isSelected ? 2.0 : 0.0,
        ),
      ),
      color: color ?? Colors.white,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: cardBorderRadius,
        child: Padding(
          padding: padding!,
          child: child,
        ),
      ),
    );
  }
}

/// 帶有標題和圖標的卡片標題組件
class CardTitle extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color? iconColor;
  final Widget? trailing;

  const CardTitle({
    super.key,
    required this.title,
    required this.icon,
    this.iconColor,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          color: iconColor ?? AppColors.royalIndigo,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (trailing != null) trailing!,
      ],
    );
  }
}
