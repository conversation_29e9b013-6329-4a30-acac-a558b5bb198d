import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../models/aspect_info.dart';
import '../models/chart_type.dart';
import '../models/firdaria_data.dart';
import '../models/planet_position.dart';
import '../utils/zodiac_symbols.dart';
import 'base_chart_painter.dart';

PlanetPosition? _lastHitPlanet;

/// 法達盤繪製器
class FirdariaChartPainter extends BaseChartPainter {
  final List<PlanetPosition> planets;
  final List<AspectInfo> aspects;
  final HouseCuspData housesData;
  final ChartType chartType;

  final List<FirdariaData> firdariaData;
  final DateTime birthDate;
  final bool isDaytime;

  // 定義各個圓環的半徑比例常數
  // 縮小本命盤的半徑，以騰出空間給法達盤使用
  static const double OUTER_CIRCLE_RADIUS_RATIO = 0.8; // 最外圈
  static const double ZODIAC_CIRCLE_RADIUS_RATIO = 0.725; // 星座符號圈
  static const double INNER_CIRCLE_RADIUS_RATIO = 0.65; // 內圈
  static const double HOUSE_NUMBER_RADIUS_RATIO = 0.6; // 宮位數字圈
  static const double HOUSE_CIRCLE_RADIUS_RATIO = 0.55; // 宮位圈

  static const double PLANET_CIRCLE_RADIUS_RATIO = 0.5; // 行星位置圈
  static const double PLANET_SYMBOL_RADIUS_RATIO = 0.45; // 行星符號圈
  static const double PLANET_DOT_RADIUS_RATIO = 0.35; // 行星點圈
  static const double ASPECT_LINE_RADIUS_RATIO = 0.35; // 相位線圈

  // 法達盤圓環半徑比例，在本命盤外圈增加兩圈
  static const double FIRDARIA_OUTER_RADIUS_RATIO = 1.0; // 法達盤外圈（次星圈）
  static const double FIRDARIA_INNER_RADIUS_RATIO = 0.9; // 法達盤內圈（次星圈）
  static const double FIRDARIA_MAIN_OUTER_RADIUS_RATIO = 0.8; // 法達盤主星外圈
  static const double FIRDARIA_MAIN_INNER_RADIUS_RATIO = 0.8; // 法達盤主星內圈

  // 定義日盤和夜盤的行星順序及年數
  static const List<Map<String, dynamic>> DAY_FIRDARIA_SEQUENCE = [
    {"planet": "Sun", "years": 10},
    {"planet": "Venus", "years": 8},
    {"planet": "Mercury", "years": 13},
    {"planet": "Moon", "years": 9},
    {"planet": "Saturn", "years": 11},
    {"planet": "Jupiter", "years": 12},
    {"planet": "Mars", "years": 7},
    {"planet": "North Node", "years": 3},
    {"planet": "South Node", "years": 2},
  ];

  static const List<Map<String, dynamic>> NIGHT_FIRDARIA_SEQUENCE = [
    {"planet": "Moon", "years": 9},
    {"planet": "Saturn", "years": 11},
    {"planet": "Jupiter", "years": 12},
    {"planet": "Mars", "years": 7},
    {"planet": "Sun", "years": 10},
    {"planet": "Venus", "years": 8},
    {"planet": "Mercury", "years": 13},
    {"planet": "North Node", "years": 3},
    {"planet": "South Node", "years": 2},
  ];

  FirdariaChartPainter(
    this.planets,
    this.aspects, {
    required this.housesData,
    required this.chartType,
    required this.firdariaData,
    required this.birthDate,
    required this.isDaytime,
    this.selectedPeriodIndex,
  }) : super(
          aspects: aspects,
          housesData: housesData,
          chartType: chartType,
        );

  // 選中的主限週期索引
  int? selectedPeriodIndex;

  // 獲取實際使用的上升點度數
  double get _effectiveAscendantDegree => housesData.ascmc[0];

  // 存儲畫布大小
  late Size size;

  @override
  void paint(Canvas canvas, Size size) {
    // 存儲畫布大小，供其他方法使用
    this.size = size;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    deltaAngle = 180 - housesData.cusps[1];

    // 繪製基本圓圈
    _drawCircles(canvas, center, radius);

    // 繪製星座分隔線
    _drawZodiacLines(canvas, center, radius);

    // 繪製宮位分隔線
    _drawHouseLines(canvas, center, radius);

    // 繪製宮位數字
    _drawHouseNumbers(canvas, center, radius);

    // 繪製星座符號
    _drawZodiacSymbols(canvas, center, radius);

    // 繪製相位線
    _drawAspectLines(canvas, center, radius);

    // 繪製行星符號
    drawPlanetSymbols(canvas, center, radius);

    // 如果是法達盤，繪製法達盤主限週期
    if (chartType == ChartType.firdaria && firdariaData.isNotEmpty) {
      // 繪製法達盤次星週期（外圈）
      _drawFirdariaDeputyPeriods(canvas, center, radius);

      // 繪製選中的法達盤主星週期（內圈）
      if (selectedPeriodIndex != null &&
          selectedPeriodIndex! < firdariaData.length) {
        _drawFirdariaPrimaryPeriods(
            canvas, center, radius, firdariaData[selectedPeriodIndex!]);
      }
    }
  }

  // 繪製基本圓圈
  @override
  void _drawCircles(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 繪製外圈
    canvas.drawCircle(
      center,
      radius * OUTER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    paint.strokeWidth = 1.0;

    // 繪製內圈
    canvas.drawCircle(
      center,
      radius * INNER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製宮位圈
    canvas.drawCircle(
      center,
      radius * HOUSE_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製行星點圈
    canvas.drawCircle(
      center,
      radius * PLANET_DOT_RADIUS_RATIO,
      paint,
    );
  }

  // 繪製星座分隔線
  void _drawZodiacLines(Canvas canvas, Offset center, double radius) {
    final zodiacLinePaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0);
      final startPoint =
          getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
      final endPoint =
          getPointByAngle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, angle);
      canvas.drawLine(startPoint, endPoint, zodiacLinePaint);
    }
  }

  // 繪製宮位分隔線
  void _drawHouseLines(Canvas canvas, Offset center, double radius) {
    final houseLinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // 如果有宮位數據，使用實際的宮位位置
    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final houseAngle = (housesData.cusps[i]);
        final startPoint = getPointByAngle(
            center, radius * ASPECT_LINE_RADIUS_RATIO, houseAngle);
        final endPoint = getPointByAngle(
            center, radius * INNER_CIRCLE_RADIUS_RATIO, houseAngle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    } else {
      // 如果沒有宮位數據，使用等宮系統（每個宮位30度）
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位角度
        // 上升點位置是第一宮的起始位置
        final angle = ((i * 30.0) + _effectiveAscendantDegree);
        final startPoint =
            getPointByAngle(center, radius * HOUSE_CIRCLE_RADIUS_RATIO, angle);
        final endPoint =
            getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    }
  }

  // 繪製宮位數字
  void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
    const houseTextStyle = TextStyle(
      color: Colors.black,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    final housePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final nextHouse = i < 12 ? i + 1 : 1;
        final houseAngle = housesData.cusps[i];
        var nextHouseAngle = housesData.cusps[nextHouse];

        // 計算宮位中間點的角度
        if (nextHouseAngle < houseAngle) {
          nextHouseAngle += 360.0;
        }
        double midAngle = (houseAngle + nextHouseAngle) / 2;
        if (nextHouse == 1 && houseAngle > nextHouseAngle) {
          midAngle = (houseAngle + nextHouseAngle + 360) / 2;
          if (midAngle >= 360) midAngle -= 360;
        }

        final position = getPointByAngle(
            center, radius * HOUSE_NUMBER_RADIUS_RATIO, midAngle);

        housePainter.text = TextSpan(
          text: '$i',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    } else {
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位數字位置
        final angle = ((i * 30.0) + 15.0 + _effectiveAscendantDegree);
        final position =
            getPointByAngle(center, radius * HOUSE_NUMBER_RADIUS_RATIO, angle);

        housePainter.text = TextSpan(
          text: '${i + 1}',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    }
  }

  // 繪製星座符號
  void _drawZodiacSymbols(Canvas canvas, Offset center, double radius) {
    final zodiacPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0 + 15.0);
      final position =
          getPointByAngle(center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, angle);

      final zodiacTextStyle = TextStyle(
          color: ZodiacSymbols.getZodiacColor(signs[i]),
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFeatures: const [FontFeature.disable('liga')],
          // 禁用字型聯合顯示，防止變成 Emoji
          fontFamily: 'astro_one_font');

      zodiacPainter.text = TextSpan(
        text: ZodiacSymbols.ZODIAC_SYMBOLS[signs[i]] ?? '?',
        style: zodiacTextStyle,
      );
      zodiacPainter.layout();
      zodiacPainter.paint(
        canvas,
        position.translate(-zodiacPainter.width / 2, -zodiacPainter.height / 2),
      );
    }
  }

  // 繪製相位線
  void _drawAspectLines(Canvas canvas, Offset center, double radius) {
    for (final aspect in aspects) {
      if (aspect.aspect == '合相') {
        continue;
      }
      final planet1 = aspect.planet1;
      final planet2 = aspect.planet2;

      final angle1 = (planet1.longitude);
      final angle2 = (planet2.longitude);

      // 使用新的 getPointByAngle 方法
      final position1 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle1);
      final position2 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle2);

      final aspectPaint = Paint()
        ..color = ZodiacSymbols.getAspectColor(aspect.symbol)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      // 繪製相位線
      canvas.drawLine(position1, position2, aspectPaint);

      // 在相位線中間繪製相位符號
      final midPoint = Offset(
        (position1.dx + position2.dx) / 2,
        (position1.dy + position2.dy) / 2,
      );

      final aspectTextStyle = TextStyle(
        color: ZodiacSymbols.getAspectColor(aspect.symbol),
        fontSize: 14,
        fontFamily: 'astro_one_font',
      );

      final aspectPainter = TextPainter(
        text: TextSpan(
          text: aspect.symbol,
          style: aspectTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      aspectPainter.layout();
      aspectPainter.paint(
        canvas,
        midPoint.translate(-aspectPainter.width / 2, -aspectPainter.height / 2),
      );
    }
  }

  List<Offset> existingPositions = [];

  // 存儲行星的位置和大小信息，用於點擊檢測
  final List<Map<String, dynamic>> _planetPositions = [];

  /// 獲取最後點擊的行星
  PlanetPosition? getLastHitPlanet() {
    return _lastHitPlanet;
  }

  /// 檢測點擊位置是否在行星上
  PlanetPosition? hitTestPlanet(Offset tapPosition) {
    // 如果沒有行星位置記錄，返回 null
    if (_planetPositions.isEmpty) {
      print('沒有行星位置記錄');
      return null;
    }

    print('檢測點擊位置: $tapPosition');
    print('行星位置記錄數量: ${_planetPositions.length}');

    // 尋找最接近點擊位置的行星
    double minDistance = 10; //double.infinity;

    // 檢查每個行星
    for (final planetInfo in _planetPositions) {
      final position = planetInfo['position'] as Offset;
      final width = planetInfo['width'] as double;
      final height = planetInfo['height'] as double;
      final planet = planetInfo['planet'] as PlanetPosition;

      print('行星 ${planet.name} 位置: $position, 寬度: $width, 高度: $height');

      // 計算點擊位置與行星位置的距離
      final double distance = (tapPosition - position).distance;
      print('行星 ${planet.name} 與點擊位置的距離: $distance');

      // 計算點擊位置是否在行星符號範圍內
      // 增加點擊區域的大小，使行星更容易被點擊
      const double hitAreaExpansion = 1.0; // 增加點擊區域
      final bool isHit =
          tapPosition.dx >= position.dx - width / 2 - hitAreaExpansion &&
              tapPosition.dx <= position.dx + width / 2 + hitAreaExpansion &&
              tapPosition.dy >= position.dy - height / 2 - hitAreaExpansion &&
              tapPosition.dy <= position.dy + height / 2 + hitAreaExpansion;

      print('行星 ${planet.name} 是否在點擊範圍內: $isHit');

      // 如果在點擊範圍內且距離最小，則更新最接近的行星
      if (isHit && distance < minDistance) {
        minDistance = distance;
        _lastHitPlanet = planet;
        print('更新最接近的行星: ${planet.name}, 距離: $minDistance');
      }
    }

    if (_lastHitPlanet != null) {
      print('找到最接近的行星: ${_lastHitPlanet!.name}');
      return _lastHitPlanet;
    }
    _lastHitPlanet = null;
    print('未找到行星');
    return null;
  }

  /// 實現 CustomPainter 的 hitTest 方法
  @override
  bool? hitTest(Offset position) {
    // 先嘗試直接檢測點擊行星
    if (_planetPositions.isNotEmpty) {
      // 使用我們的自定義方法檢測點擊
      _lastHitPlanet = hitTestPlanet(position);

      // 如果點擊到行星，返回 true
      if (_lastHitPlanet != null) {
        print('直接點擊檢測到行星: ${_lastHitPlanet!.name}');
        return true;
      }
    }

    // 如果沒有點擊到行星，檢測是否點擊到法達盤主限週期
    if (chartType == ChartType.firdaria && firdariaData.isNotEmpty) {
      final center = Offset(size.width / 2, size.height / 2);
      final radius = size.width / 2;
      final hitPeriod = _hitTestFirdariaDeputyPeriods(position, center, radius);
      if (hitPeriod != null) {
        // 找到點擊的主限週期的索引
        final index = firdariaData.indexOf(hitPeriod);
        if (index >= 0) {
          // 更新選中的主限週期索引
          selectedPeriodIndex = index;
          return true;
        }
      }
    }

    return false;
  }

  bool isOverlapping(
      Offset position1, double size1, Offset position2, double size2) {
    return (position1.dx - size1 / 2 < position2.dx + size2 / 2 &&
        position1.dx + size1 / 2 > position2.dx - size2 / 2 &&
        position1.dy - size1 / 2 < position2.dy + size2 / 2 &&
        position1.dy + size1 / 2 > position2.dy - size2 / 2);
  }

  // 識別行星群組，將相近的行星分組
  List<List<PlanetPosition>> _identifyPlanetClusters(
      List<PlanetPosition> planets, double clusterThreshold) {
    final List<List<PlanetPosition>> clusters = [];
    final List<PlanetPosition> sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    if (sortedPlanets.isEmpty) return clusters;

    List<PlanetPosition> currentCluster = [sortedPlanets.first];

    for (int i = 1; i < sortedPlanets.length; i++) {
      final current = sortedPlanets[i];
      final previous = sortedPlanets[i - 1];

      // 計算兩個行星之間的角度差
      double diff = current.longitude - previous.longitude;
      if (diff < 0) diff += 360; // 處理跨越0度的情況

      // 如果角度差小於閾值，將當前行星加入當前群組
      if (diff <= clusterThreshold) {
        currentCluster.add(current);
      } else {
        // 否則，結束當前群組並開始新群組
        clusters.add(List.from(currentCluster));
        currentCluster = [current];
      }
    }

    // 處理最後一個群組
    if (currentCluster.isNotEmpty) {
      clusters.add(currentCluster);
    }

    // 檢查第一個和最後一個群組是否應該合併（處理環形結構）
    if (clusters.length > 1) {
      final firstCluster = clusters.first;
      final lastCluster = clusters.last;

      final firstPlanet = firstCluster.first;
      final lastPlanet = lastCluster.last;

      double diff = firstPlanet.longitude + 360 - lastPlanet.longitude;
      if (diff <= clusterThreshold) {
        // 合併第一個和最後一個群組
        clusters.first = [...lastCluster, ...firstCluster];
        clusters.removeLast();
      }
    }

    return clusters;
  }

  // 計算群組內行星的最佳間距
  Map<String, double> _calculateOptimalPositions(
      List<List<PlanetPosition>> clusters) {
    final Map<String, double> adjustedPositions = {};

    // 處理每個群組
    for (final cluster in clusters) {
      if (cluster.length <= 1) {
        // 單個行星不需要調整
        adjustedPositions[cluster.first.name] = cluster.first.longitude;
        continue;
      }

      // 計算群組的角度範圍
      final firstPlanet = cluster.first;
      final lastPlanet = cluster.last;
      double startAngle = firstPlanet.longitude;
      double endAngle = lastPlanet.longitude;

      // 處理跨越0度的情況
      if (endAngle < startAngle) {
        endAngle += 360;
      }

      // 計算群組的角度範圍
      double rangeAngle = endAngle - startAngle;

      // 計算每個行星之間的最小間距（至少3度）
      double minSpacing = 9.0;
      double requiredSpace = (cluster.length - 1) * minSpacing;

      // 如果群組的角度範圍小於所需空間，擴展範圍
      if (rangeAngle < requiredSpace) {
        // 擴展範圍，使每個行星之間有足夠的空間
        double expansion = requiredSpace - rangeAngle;
        startAngle -= expansion / 2;
        endAngle += expansion / 2;
        rangeAngle = requiredSpace;
      }

      // 計算每個行星之間的間距
      double spacing = rangeAngle / (cluster.length - 1);

      // 分配行星位置
      for (int i = 0; i < cluster.length; i++) {
        double newPosition = startAngle + i * spacing;
        // 確保角度在0-360範圍內
        while (newPosition >= 360) {
          newPosition -= 360;
        }
        while (newPosition < 0) {
          newPosition += 360;
        }

        adjustedPositions[cluster[i].name] = newPosition;
      }
    }

    return adjustedPositions;
  }

  // 繪製行星符號
  @override
  void drawPlanetSymbols(Canvas canvas, Offset center, double radius) {
    final Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.black;

    if (planets.isEmpty) return;

    // 清空行星位置記錄，準備重新記錄
    _planetPositions.clear();

    // 識別行星群組（相距10度以內的行星視為一組）
    final clusters = _identifyPlanetClusters(planets, 10.0);

    // 計算每個行星的最佳位置
    final adjustedPositions = _calculateOptimalPositions(clusters);

    // 按照角度排序行星
    existingPositions.clear();
    final sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    for (final planet in sortedPlanets) {
      // 使用調整後的角度（如果有）
      var angle = adjustedPositions[planet.name] ?? planet.longitude;

      // 繪製行星，半徑為 2 的圓形
      paint.color = planet.color;
      var positionCircle = getPointByAngle(
          center, radius * PLANET_DOT_RADIUS_RATIO, planet.longitude);
      canvas.drawCircle(positionCircle, 2, paint);

      // 使用調整後的角度計算行星符號位置
      var position =
          getPointByAngle(center, radius * PLANET_SYMBOL_RADIUS_RATIO, angle);

      // 繪製行星符號
      final planetTextStyle = TextStyle(
          color: planet.color,
          fontSize: 16,
          fontWeight: FontWeight.normal,
          // fontFeatures: const [FontFeature.disable('liga')],
          // 禁用字型聯合顯示，防止變成 Emoji
          fontFamily: 'astro_one_font');
      final planetPainter = TextPainter(
        text: TextSpan(
          text: planet.symbol,
          style: planetTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      planetPainter.layout();

      // 計算行星符號的實際位置
      final symbolPosition = position.translate(
          -planetPainter.width / 2, -planetPainter.height / 2);

      planetPainter.paint(
        canvas,
        symbolPosition,
      );

      // 記錄行星的位置和大小信息，用於點擊檢測
      _planetPositions.add({
        'planet': planet,
        'position': position,
        'width': planetPainter.width * 1.5, // 增加點擊區域
        'height': planetPainter.height * 1.5, // 增加點擊區域
        'angle': angle, // 記錄行星角度
        'longitude': planet.longitude, // 記錄行星經度
      });

      print(
          '記錄行星 ${planet.name} 位置: $position, 角度: $angle, 經度: ${planet.longitude}, 寬度: ${planetPainter.width}, 高度: ${planetPainter.height}');
      var positionLine = getPointByAngle(
          center, radius * PLANET_SYMBOL_RADIUS_RATIO * 0.92, angle);
      // 繪製連接線
      canvas.drawLine(
        positionCircle,
        positionLine,
        Paint()
          ..color = planet.color
          ..strokeWidth = 0.5,
      );

      // 如果行星逆行，繪製逆行標記
      if (planet.longitudeSpeed < 0) {
        var retroPosition = getPointByAngle(
            center, radius * PLANET_SYMBOL_RADIUS_RATIO * 1.15, angle);
        final retroPainter = TextPainter(
          text: const TextSpan(
            text: '℞',
            style: TextStyle(
              color: Colors.red,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        retroPainter.layout();
        retroPainter.paint(
          canvas,
          retroPosition.translate(
              -retroPainter.width / 2, -retroPainter.height / 2),
        );
      }
    }
  }

  // 根據角度計算點的位置
  // 參考 Kotlin 函數: getPointByAngle
  @override
  Offset getPointByAngle(Offset pt, double r, double angle) {
    double angleTemp = angle + deltaAngle; // 添加偏移角度
    double x = pt.dx + r * cos(angleTemp * pi / 180); // 轉換為弧度並計算 x 坐標
    double y = pt.dy - r * sin(angleTemp * pi / 180); // 轉換為弧度並計算 y 坐標
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  @override
  double getAspectAngle1(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet1);
    return planet.longitude;
  }

  @override
  double getAspectAngle2(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet2);
    return planet.longitude;
  }

  /// 繪製年齡進度弧形
  void _drawAgeArc(Canvas canvas, Offset center, double radius, double angle) {
    final paint = Paint()
      ..strokeWidth = 10
      ..color = Colors.red.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..isAntiAlias = true;

    // final rect = Rect.fromCircle(center: center, radius: radius);

    // 繪製已過年齡的弧形（紅色）
    // canvas.drawArc(rect, -pi / 2, angle, false, paint);

    // 繪製未過年齡的弧形（藍色）
    paint.color = Colors.blue.withOpacity(0.3);
    // canvas.drawArc(rect, -pi / 2 + angle, 2 * pi - angle, false, paint);

    final outerRadius = radius * FIRDARIA_OUTER_RADIUS_RATIO;
    final innerRadius = radius * FIRDARIA_INNER_RADIUS_RATIO;
    final mainRadius = radius * OUTER_CIRCLE_RADIUS_RATIO;

    // 繪製終點線條
    final endAngle = -pi / 2 + angle; // 紅色弧形的結束角度
    final start = getPoint(center, mainRadius, endAngle); // 線內端
    final end = getPoint(center, outerRadius, endAngle); // 線外端

    final linePaint = Paint()
      ..strokeWidth = 2
      ..color = Colors.red
      ..style = PaintingStyle.stroke;

    /// 繪製年齡線
    canvas.drawLine(start, end, linePaint);
  }

  Offset getPoint(Offset center, double radius, double angle) {
    return Offset(
      center.dx + radius * cos(angle),
      center.dy + radius * sin(angle),
    );
  }

  /// 繪製弧
  void _drawArc(
    Canvas canvas,
    Offset center,
    double innerRadius,
    double outerRadius,
    double angle, {
    Color progressColor = Colors.black,
    Color remainingColor = Colors.blue,
    double opacity = 0.3,
    bool isDashed = false,
  }) {
    final paint = Paint()
      ..strokeWidth = outerRadius - innerRadius
      ..color = progressColor.withOpacity(opacity)
      ..style = PaintingStyle.stroke
      ..isAntiAlias = true;

    // 如果需要虛線，設置虛線效果
    // if (isDashed) {
    //   paint.strokeCap = StrokeCap.round;
    //   // 設置虛線效果（Flutter 2.0+ 支援）
    //   // 如果不支援，可以手動繪製虛線
    //   paint.strokeJoin = StrokeJoin.round;
    // }

    final x = center.dx - (outerRadius + innerRadius) / 2;
    final y = center.dy - (outerRadius + innerRadius) / 2;
    final width = (outerRadius + innerRadius);
    final height = (outerRadius + innerRadius);
    final oval = Rect.fromLTWH(x, y, width, height);

    canvas.drawArc(oval, -pi / 2, angle, false, paint);

    final remainingPaint = Paint()
      ..strokeWidth = outerRadius - innerRadius
      ..color = remainingColor.withOpacity(opacity)
      ..style = PaintingStyle.stroke
      ..isAntiAlias = true;

    // 如果需要虛線，設置虛線效果
    // if (isDashed) {
    //   remainingPaint.strokeCap = StrokeCap.round;
    //   remainingPaint.strokeJoin = StrokeJoin.round;
    // }

    canvas.drawArc(
        oval, -pi / 2 + angle, 2 * pi - angle, false, remainingPaint);
  }

  /// 繪製法達盤次星週期（外圈）
  void _drawFirdariaDeputyPeriods(Canvas canvas, Offset center, double radius) {
    if (firdariaData.isEmpty) return;

    final outerRadius = radius * FIRDARIA_OUTER_RADIUS_RATIO;
    final innerRadius = radius * FIRDARIA_INNER_RADIUS_RATIO;
    final mainRadius = radius * OUTER_CIRCLE_RADIUS_RATIO;

    final isDaytime = _isDayChart();
    // _drawChartTitle(canvas, center, outerRadius, isDaytime);

    final ageAngle = _calculateAgeAngle();
    _drawAgeArc(canvas, center, radius, ageAngle);
    _drawProgressArcs(
        canvas, center, mainRadius, innerRadius, outerRadius, ageAngle);
    _drawBaseCircles(canvas, center, mainRadius, innerRadius, outerRadius);

    final sortedPeriods = _sortFirdariaByDayOrNight(isDaytime);

    _drawFirdariaPeriods(
      canvas,
      center,
      sortedPeriods,
      birthDate,
      mainRadius,
      innerRadius,
      outerRadius,
    );
  }

  // 判斷日夜盤
  bool _isDayChart() {
    final sunPlanet =
        planets.firstWhere((p) => p.name == 'Sun', orElse: () => planets.first);
    return sunPlanet.house > 6;
  }

  // 繪製圖表標題
  void _drawChartTitle(
      Canvas canvas, Offset center, double outerRadius, bool isDaytime) {
    final titlePaint = TextPainter(
      text: TextSpan(
        text: isDaytime ? '日盤法達' : '夜盤法達',
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    titlePaint.layout();
    titlePaint.paint(
      canvas,
      Offset(center.dx - titlePaint.width / 2, center.dy - outerRadius - 20),
    );
  }

  // 計算目前年齡角度
  double _calculateAgeAngle() {
    final currentDate = DateTime.now();
    final birthdayTime = currentDate.difference(birthDate).inMilliseconds;
    const year75 = 75 * 365 * 24 * 60 * 60 * 1000;
    final age = birthdayTime / year75;
    return age * 2 * pi;
  }

  // 繪製年齡進度弧與次星弧
  void _drawProgressArcs(Canvas canvas, Offset center, double mainRadius,
      double innerRadius, double outerRadius, double ageAngle) {
    _drawArc(
      canvas,
      center,
      mainRadius,
      innerRadius,
      ageAngle,
      progressColor: Colors.purple,
      remainingColor: Colors.teal,
      opacity: 0.2,
    );

    _drawArc(
      canvas,
      center,
      innerRadius,
      outerRadius,
      ageAngle,
      progressColor: Colors.red,
      remainingColor: Colors.blue,
      opacity: 0.3,
      isDashed: true,
    );
  }

  // 繪製基本圓圈
  void _drawBaseCircles(Canvas canvas, Offset center, double mainRadius,
      double innerRadius, double outerRadius) {
    final circlePaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = Colors.black
      ..strokeWidth = 1.0;

    canvas.drawCircle(center, outerRadius, circlePaint);
    canvas.drawCircle(center, innerRadius, circlePaint);
    canvas.drawCircle(center, mainRadius, circlePaint);
  }

  // 排序法達資料（依日夜盤）
  List<FirdariaData> _sortFirdariaByDayOrNight(bool isDaytime) {
    final sorted = List<FirdariaData>.from(firdariaData);
    final dayOrder = [
      'Sun',
      'Venus',
      'Mercury',
      'Moon',
      'Saturn',
      'Jupiter',
      'Mars',
      'North Node',
      'South Node'
    ];
    final nightOrder = [
      'Moon',
      'Saturn',
      'Jupiter',
      'Mars',
      'Sun',
      'Venus',
      'Mercury',
      'North Node',
      'South Node'
    ];
    final order = isDaytime ? dayOrder : nightOrder;

    sorted.sort((a, b) =>
        order.indexOf(a.majorPlanetName) - order.indexOf(b.majorPlanetName));
    return sorted;
  }

  // 畫主次星區段與符號
  void _drawFirdariaPeriods(
    Canvas canvas,
    Offset center,
    List<FirdariaData> sortedPeriods,
    DateTime birthDate,
    double mainRadius,
    double innerRadius,
    double outerRadius,
  ) {
    double angle = -pi / 2;
    const oneStep = 2 * pi / 75.0;

    for (final period in sortedPeriods) {
      final tempAngle = oneStep * period.durationYears;

      _drawMajorPeriodLine(canvas, center, angle, innerRadius, mainRadius);

      if (period.subPeriods.isNotEmpty &&
          period.majorPlanetName != '北交點' &&
          period.majorPlanetName != '南交點') {
        _drawSubPeriods(canvas, center, period, angle, tempAngle, innerRadius,
            outerRadius, mainRadius, sortedPeriods);
      }

      if (period.majorPlanetName == '北交點' || period.majorPlanetName == '南交點') {
        double deputyAngle = angle + (tempAngle - tempAngle / 2);
        _drawFirdariaDeputyPlanetSymbol(
          canvas,
          center,
          deputyAngle,
          (outerRadius + innerRadius) / 2,
          period.majorPlanetSymbol,
          period.majorPlanetColor,
        );
        _drawMajorPeriodLine(
            canvas, center, angle + tempAngle, innerRadius, outerRadius);
      }

      final startAge = _calculateAge(birthDate, period.startDate);
      final endAge = _calculateAge(birthDate, period.endDate);
      print('法達盤主星週期：$startAge歲 - $endAge歲');

      final planetAngle = (angle + tempAngle + angle) / 2;

      _drawFirdariaPrimaryPlanetSymbol(
        canvas,
        center,
        planetAngle,
        (innerRadius + mainRadius) / 2,
        period.majorPlanetSymbol,
        period.majorPlanetColor,
      );

      // 繪製年齡範圍
      _drawAgeRangeText(
          canvas, center, angle, (innerRadius + mainRadius) / 2, '$startAge歲');

      if (selectedPeriodIndex != null &&
          firdariaData[selectedPeriodIndex!] == period) {
        _lastHitPeriod = period;
      }

      angle += tempAngle;
      if (angle > pi) angle -= 2 * pi;
    }
  }

  // 畫次星區段與符號
  void _drawMajorPeriodLine(Canvas canvas, Offset center, double angle,
      double innerRadius, double mainRadius) {
    final pt1 = Offset(center.dx + innerRadius * cos(angle),
        center.dy + innerRadius * sin(angle));
    final pt2 = Offset(center.dx + mainRadius * cos(angle),
        center.dy + mainRadius * sin(angle));

    final linePaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = Colors.black.withOpacity(0.7)
      ..strokeWidth = 1.0;

    canvas.drawLine(pt1, pt2, linePaint);
  }

  // 畫次星區段與符號
  void _drawSubPeriods(
    Canvas canvas,
    Offset center,
    FirdariaData period,
    double baseAngle,
    double arcAngle,
    double innerRadius,
    double outerRadius,
    double mainRadius,
    List<FirdariaData> yearPlanet,
  ) {
    double step = arcAngle / 7;
    double lineAngle = baseAngle - step;
    _drawMajorPeriodLine(canvas, center, baseAngle, innerRadius, outerRadius);
    int indexDeputy = yearPlanet.indexOf(period);

    for (int i = 0; i < 7 && i < period.subPeriods.length; i++) {
      final deputy = period.subPeriods[i];
      double deputyAngle = baseAngle + (((i + 1) * step) - step / 2);
      _drawFirdariaDeputyPlanetSymbol(
        canvas,
        center,
        deputyAngle,
        (outerRadius + innerRadius) / 2,
        deputy.subPlanetSymbol,
        deputy.subPlanetColor,
      );

      final tempLine = lineAngle;
      lineAngle = _calculateAngle(baseAngle, step, i + 1);
      deputyAngle = (lineAngle + tempLine) / 2;
      _drawMajorPeriodLine(canvas, center, lineAngle, innerRadius, outerRadius);
      indexDeputy++;
      if (indexDeputy >= yearPlanet.length) indexDeputy = 0;
    }
  }

  /// 計算角度，將行星位置轉換為角度
  double _calculateAngle(double startAngle, double step, int index) {
    double angle = startAngle + step * index;
    // 確保角度在 -pi 到 pi 的範圍內
    if (angle < -pi) {
      angle += 2 * pi;
    }
    return angle;
  }

  /// 繪製法達盤次星符號
  void _drawFirdariaDeputyPlanetSymbol(Canvas canvas, Offset center,
      double angle, double radius, String symbol, Color color) {
    final position = Offset(
      center.dx + radius * cos(angle),
      center.dy + radius * sin(angle),
    );
    _drawText(
      canvas,
      position,
      symbol,
      fontSize: 14,
      color: color,
      bold: false,
    );
  }

  /// 繪製法達盤主星符號
  void _drawFirdariaPrimaryPlanetSymbol(Canvas canvas, Offset center,
      double angle, double radius, String symbol, Color color) {
    final position = Offset(
      center.dx + radius * cos(angle),
      center.dy + radius * sin(angle),
    );

    // 繪製行星符號
    _drawText(
      canvas,
      position,
      symbol,
      fontSize: 16,
      color: color,
      bold: true,
    );

    // 如果是交點，在外圈也繪製一個
    if (symbol == '☊' || symbol == '☋') {
      final outerPosition = Offset(
        center.dx + ((radius + center.dx) / 2) * cos(angle),
        center.dy + ((radius + center.dy) / 2) * sin(angle),
      );

      _drawText(
        canvas,
        outerPosition,
        symbol,
        fontSize: 16,
        color: color,
        bold: true,
      );
    }
  }

  /// 獨立繪製年齡範圍文字的方法
  void _drawAgeRangeText(Canvas canvas, Offset center, double angle,
      double radius, String ageRange) {
    final agePosition = Offset(
      center.dx + (radius) * cos(angle),
      center.dy + (radius) * sin(angle),
    );

    _drawText(
      canvas,
      agePosition,
      ageRange,
      fontSize: 6,
      color: Colors.black,
      bold: false,
    );
  }

  /// 繪製不旋轉的文字
  void _drawText(
    Canvas canvas,
    Offset position,
    String text, {
    double fontSize = 12,
    Color color = Colors.yellow,
    bool bold = false,
  }) {
    canvas.save();
    canvas.translate(position.dx, position.dy);

    // 繪製行星符號
    final planetTextStyle = TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: bold ? FontWeight.bold : FontWeight.normal,
      fontFamily: 'astro_one_font',
    );

    final planetPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: planetTextStyle,
      ),
      textDirection: TextDirection.ltr,
    );

    print("_drawText $text");

    planetPainter.layout();
    planetPainter.paint(
      canvas,
      Offset(-planetPainter.width / 2, -planetPainter.height / 2),
    );

    canvas.restore();
  }

  /// 計算年齡
  int _calculateAge(DateTime birthDate, DateTime currentDate) {
    int age = currentDate.year - birthDate.year;
    if (currentDate.month < birthDate.month ||
        (currentDate.month == birthDate.month &&
            currentDate.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// 存儲最後點擊的法達盤週期
  FirdariaData? _lastHitPeriod;

  /// 獲取最後點擊的法達盤週期
  FirdariaData? getLastHitPeriod() {
    return _lastHitPeriod;
  }

  /// 繪製法達盤主星週期（內圈）
  void _drawFirdariaPrimaryPeriods(
      Canvas canvas, Offset center, double radius, FirdariaData period) {
    if (period.subPeriods.isEmpty) return;

    // 法達盤主星圈的半徑
    final outerRadius = radius * FIRDARIA_MAIN_OUTER_RADIUS_RATIO;
    final innerRadius = radius * FIRDARIA_MAIN_INNER_RADIUS_RATIO;

    // 計算總天數
    final totalDays = period.endDate.difference(period.startDate).inDays;
    if (totalDays <= 0) return; // 避免除以零的錯誤

    // 起始角度（從正上方開始，順時針方向）
    double startAngle = -pi / 2;

    // 繪製每個子週期
    for (final subPeriod in period.subPeriods) {
      // 計算該子週期佔的角度
      final days = subPeriod.endDate.difference(subPeriod.startDate).inDays;
      if (days <= 0) continue; // 跳過無效的子週期

      final sweepAngle = 2 * pi * days / totalDays;

      // 設置畫筆
      final paint = Paint()
        ..style = PaintingStyle.fill
        ..color = subPeriod.subPlanetColor.withOpacity(0.3);

      // 如果是當前子週期，使用更深的顏色
      if (subPeriod.isCurrent) {
        paint.color = subPeriod.subPlanetColor.withOpacity(0.6);
      }

      // 繪製扇形
      // final path = Path()
      //   ..moveTo(center.dx, center.dy)
      //   ..arcTo(
      //     Rect.fromCircle(center: center, radius: outerRadius),
      //     startAngle,
      //     sweepAngle,
      //     false,
      //   )
      //   ..arcTo(
      //     Rect.fromCircle(center: center, radius: innerRadius),
      //     startAngle + sweepAngle,
      //     -sweepAngle,
      //     false,
      //   )
      //   ..close();
      //
      // canvas.drawPath(path, paint);

      // 更新起始角度
      startAngle += sweepAngle;
    }
  }

  /// 測試點擊法達盤次星週期（外圈）
  FirdariaData? _hitTestFirdariaDeputyPeriods(
      Offset position, Offset center, double radius) {
    if (firdariaData.isEmpty) return null;

    // 使用調整後的半徑比例常數
    final outerRadius = radius * FIRDARIA_OUTER_RADIUS_RATIO;
    final innerRadius = radius * FIRDARIA_INNER_RADIUS_RATIO;

    // 計算點擊位置到中心的距離
    final distance = (position - center).distance;

    // 如果不在主限週期的環形區域內，返回 null
    if (distance < innerRadius || distance > outerRadius) return null;

    // 計算點擊位置的角度（從正上方開始，順時針方向）
    double angle = atan2(position.dy - center.dy, position.dx - center.dx);
    if (angle < 0) angle += 2 * pi;
    angle = (angle + pi / 2) % (2 * pi);

    // 計算總天數（法達盤為 75 年循環）
    final totalYears = 75.0;
    final totalDegrees = 360.0;
    final degreesPerYear = totalDegrees / totalYears;

    // 起始角度（從正上方開始，順時針方向）
    double startAngle = 0;

    // 檢查點擊位置是否在某個週期的扇形區域內
    for (final period in firdariaData) {
      // 計算該週期佔的角度
      final years = period.durationYears;
      final sweepAngle = degreesPerYear * years * pi / 180;

      // 計算該週期的結束角度
      final endAngle = startAngle + sweepAngle;

      // 檢查點擊位置是否在該週期的扇形區域內
      if (angle >= startAngle && angle < endAngle) {
        _lastHitPeriod = period;
        return period;
      }

      // 更新起始角度
      startAngle = endAngle;
    }
    return null;
  }
}
