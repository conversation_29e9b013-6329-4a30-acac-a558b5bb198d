import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';

import '../models/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import '../models/chart_type.dart';
import '../models/planet_position.dart';
import '../utils/zodiac_symbols.dart';
import 'base_chart_painter.dart';

PlanetPosition? _lastHitPlanet;

class ChartPainter extends BaseChartPainter {
  final List<PlanetPosition> planets;
  final List<AspectInfo> aspects;
  final HouseCuspData housesData;
  final ChartType chartType; // 添加星盤類型參數
  double deltaAngle = 0;

  // 定義各個圓環的半徑比例常數
  static const double OUTER_CIRCLE_RADIUS_RATIO = 1.0; // 最外圈
  static const double ZODIAC_CIRCLE_RADIUS_RATIO = 0.9; // 星座符號圈
  static const double INNER_CIRCLE_RADIUS_RATIO = 0.8; // 內圈
  static const double HOUSE_NUMBER_RADIUS_RATIO = 0.75; // 宮位數字圈
  static const double HOUSE_CIRCLE_RADIUS_RATIO = 0.7; // 宮位圈

  static const double PLANET_CIRCLE_RADIUS_RATIO = 0.65; // 行星位置圈
  static const double PLANET_SYMBOL_RADIUS_RATIO = 0.55; // 行星符號圈
  static const double PLANET_DOT_RADIUS_RATIO = 0.45; // 行星點圈
  static const double ASPECT_LINE_RADIUS_RATIO = 0.45; // 相位線圈

  ChartPainter(
    this.planets,
    this.aspects, {
    required this.housesData,
    required this.chartType, // 添加星盤類型參數
  }) : super(
          aspects: aspects,
          housesData: housesData,
          chartType: chartType,
        );

  // 獲取實際使用的上升點度數
  double get _effectiveAscendantDegree => housesData.ascmc[0];

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    deltaAngle = 180 - housesData.cusps[1];

    // 保存星盤中心和半徑，用於點擊檢測
    _chartCenter = center;
    _chartRadius = radius;

    // 繪製基本圓圈
    _drawCircles(canvas, center, radius);

    // 繪製星座分隔線
    _drawZodiacLines(canvas, center, radius);

    // 繪製宮位分隔線
    _drawHouseLines(canvas, center, radius);

    // 繪製宮位數字
    _drawHouseNumbers(canvas, center, radius);

    // 繪製星座符號
    _drawZodiacSymbols(canvas, center, radius);

    // 繪製相位線
    _drawAspectLines(canvas, center, radius);

    // 繪製行星符號
    drawPlanetSymbols(canvas, center, radius);

    // 繪製上升點標記
    // _drawAscendantMarker(canvas, center, radius);
  }

  // 繪製基本圓圈
  @override
  void _drawCircles(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 繪製外圈
    canvas.drawCircle(
      center,
      radius * OUTER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    paint.strokeWidth = 1.0;

    // 繪製內圈
    canvas.drawCircle(
      center,
      radius * INNER_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製宮位圈
    canvas.drawCircle(
      center,
      radius * HOUSE_CIRCLE_RADIUS_RATIO,
      paint,
    );

    // 繪製行星點圈
    canvas.drawCircle(
      center,
      radius * PLANET_DOT_RADIUS_RATIO,
      paint,
    );
  }

  // 繪製星座分隔線
  void _drawZodiacLines(Canvas canvas, Offset center, double radius) {
    final zodiacLinePaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0);
      final startPoint =
          getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
      final endPoint =
          getPointByAngle(center, radius * OUTER_CIRCLE_RADIUS_RATIO, angle);
      canvas.drawLine(startPoint, endPoint, zodiacLinePaint);
    }
  }

  // 繪製宮位分隔線
  void _drawHouseLines(Canvas canvas, Offset center, double radius) {
    final houseLinePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // 如果有宮位數據，使用實際的宮位位置
    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final houseAngle = (housesData.cusps[i]);
        final startPoint = getPointByAngle(
            center, radius * ASPECT_LINE_RADIUS_RATIO, houseAngle);
        final endPoint = getPointByAngle(
            center, radius * INNER_CIRCLE_RADIUS_RATIO, houseAngle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    } else {
      // 如果沒有宮位數據，使用等宮系統（每個宮位30度）
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位角度
        // 上升點位置是第一宮的起始位置
        final angle = ((i * 30.0) + _effectiveAscendantDegree);
        final startPoint =
            getPointByAngle(center, radius * HOUSE_CIRCLE_RADIUS_RATIO, angle);
        final endPoint =
            getPointByAngle(center, radius * INNER_CIRCLE_RADIUS_RATIO, angle);
        canvas.drawLine(startPoint, endPoint, houseLinePaint);
      }
    }
  }

  // 繪製宮位數字
  void _drawHouseNumbers(Canvas canvas, Offset center, double radius) {
    const houseTextStyle = TextStyle(
      color: Colors.black,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    final housePainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    if (housesData.cusps.isNotEmpty) {
      for (int i = 1; i <= 12; i++) {
        final nextHouse = i < 12 ? i + 1 : 1;
        final houseAngle = housesData.cusps[i];
        var nextHouseAngle = housesData.cusps[nextHouse];

        // 計算宮位中間點的角度
        if (nextHouseAngle < houseAngle) {
          nextHouseAngle += 360.0;
        }
        double midAngle = (houseAngle + nextHouseAngle) / 2;
        if (nextHouse == 1 && houseAngle > nextHouseAngle) {
          midAngle = (houseAngle + nextHouseAngle + 360) / 2;
          if (midAngle >= 360) midAngle -= 360;
        }

        final position = getPointByAngle(
            center, radius * HOUSE_NUMBER_RADIUS_RATIO, midAngle);

        housePainter.text = TextSpan(
          text: '$i',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    } else {
      for (int i = 0; i < 12; i++) {
        // 根據上升點位置調整宮位數字位置
        final angle = ((i * 30.0) + 15.0 + _effectiveAscendantDegree);
        final position =
            getPointByAngle(center, radius * HOUSE_NUMBER_RADIUS_RATIO, angle);

        housePainter.text = TextSpan(
          text: '${i + 1}',
          style: houseTextStyle,
        );
        housePainter.layout();
        housePainter.paint(
          canvas,
          position.translate(-housePainter.width / 2, -housePainter.height / 2),
        );
      }
    }
  }

  // 繪製星座符號
  void _drawZodiacSymbols(Canvas canvas, Offset center, double radius) {
    final zodiacPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    final signs = [
      '牡羊座',
      '金牛座',
      '雙子座',
      '巨蟹座',
      '獅子座',
      '處女座',
      '天秤座',
      '天蠍座',
      '射手座',
      '摩羯座',
      '水瓶座',
      '雙魚座'
    ];

    for (int i = 0; i < 12; i++) {
      final angle = (i * 30.0 + 15.0);
      final position =
          getPointByAngle(center, radius * ZODIAC_CIRCLE_RADIUS_RATIO, angle);

      final zodiacTextStyle = TextStyle(
          color: ZodiacSymbols.getZodiacColor(signs[i]),
          fontSize: 16,
          fontWeight: FontWeight.bold,
          fontFeatures: const [FontFeature.disable('liga')],
          // 禁用字型聯合顯示，防止變成 Emoji
          fontFamily: 'astro_one_font');

      zodiacPainter.text = TextSpan(
        text: ZodiacSymbols.ZODIAC_SYMBOLS[signs[i]] ?? '?',
        style: zodiacTextStyle,
      );
      zodiacPainter.layout();
      zodiacPainter.paint(
        canvas,
        position.translate(-zodiacPainter.width / 2, -zodiacPainter.height / 2),
      );
    }
  }

  // 繪製相位線
  void _drawAspectLines(Canvas canvas, Offset center, double radius) {
    for (final aspect in aspects) {
      if (aspect.aspect == '合相') {
        continue;
      }
      final planet1 = aspect.planet1;
      final planet2 = aspect.planet2;

      final angle1 = (planet1.longitude);
      final angle2 = (planet2.longitude);

      // 使用新的 getPointByAngle 方法
      final position1 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle1);
      final position2 =
          getPointByAngle(center, radius * ASPECT_LINE_RADIUS_RATIO, angle2);

      final aspectPaint = Paint()
        ..color = ZodiacSymbols.getAspectColor(aspect.symbol)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      // 繪製相位線
      canvas.drawLine(position1, position2, aspectPaint);

      // 在相位線中間繪製相位符號
      final midPoint = Offset(
        (position1.dx + position2.dx) / 2,
        (position1.dy + position2.dy) / 2,
      );

      final aspectTextStyle = TextStyle(
        color: ZodiacSymbols.getAspectColor(aspect.symbol),
        fontSize: 14,
        fontFamily: 'astro_one_font',
      );

      final aspectPainter = TextPainter(
        text: TextSpan(
          text: aspect.symbol,
          style: aspectTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      aspectPainter.layout();
      aspectPainter.paint(
        canvas,
        midPoint.translate(-aspectPainter.width / 2, -aspectPainter.height / 2),
      );
    }
  }

  List<Offset> existingPositions = [];

  // 存儲行星的位置和大小信息，用於點擊檢測
  final List<Map<String, dynamic>> _planetPositions = [];

  // 存儲星盤中心和半徑，用於點擊檢測
  Offset? _chartCenter;
  double? _chartRadius;

  /// 獲取最後點擊的行星
  PlanetPosition? getLastHitPlanet() {
    return _lastHitPlanet;
  }

  /// 檢測點擊位置是否在行星上
  PlanetPosition? hitTestPlanet(Offset tapPosition) {
    // 如果沒有行星位置記錄，返回 null
    if (_planetPositions.isEmpty) {
      print('沒有行星位置記錄');
      return null;
    }

    print('檢測點擊位置: $tapPosition');
    print('行星位置記錄數量: ${_planetPositions.length}');

    // 尋找最接近點擊位置的行星
    PlanetPosition? closestPlanet;
    double minDistance = 10; //double.infinity;

    // 檢查每個行星
    for (final planetInfo in _planetPositions) {
      final position = planetInfo['position'] as Offset;
      final width = planetInfo['width'] as double;
      final height = planetInfo['height'] as double;
      final planet = planetInfo['planet'] as PlanetPosition;

      print('行星 ${planet.name} 位置: $position, 寬度: $width, 高度: $height');

      // 計算點擊位置與行星位置的距離
      final double distance = (tapPosition - position).distance;
      print('行星 ${planet.name} 與點擊位置的距離: $distance');

      // 計算點擊位置是否在行星符號範圍內
      // 增加點擊區域的大小，使行星更容易被點擊
      const double hitAreaExpansion = 1.0; // 增加點擊區域
      final bool isHit =
          tapPosition.dx >= position.dx - width / 2 - hitAreaExpansion &&
              tapPosition.dx <= position.dx + width / 2 + hitAreaExpansion &&
              tapPosition.dy >= position.dy - height / 2 - hitAreaExpansion &&
              tapPosition.dy <= position.dy + height / 2 + hitAreaExpansion;

      print('行星 ${planet.name} 是否在點擊範圍內: $isHit');

      // 如果在點擊範圍內且距離最小，則更新最接近的行星
      if (isHit && distance < minDistance) {
        minDistance = distance;
        _lastHitPlanet = planet;
        print('更新最接近的行星: ${planet.name}, 距離: $minDistance');
      }
    }

    if (_lastHitPlanet != null) {
      print('找到最接近的行星: ${_lastHitPlanet!.name}');
      return _lastHitPlanet;
    }
    _lastHitPlanet = null;
    print('未找到行星');
    return null;
  }

  /// 實現 CustomPainter 的 hitTest 方法
  @override
  bool? hitTest(Offset position) {
    // if (_chartCenter == null || _chartRadius == null) {
    //   print('星盤中心或半徑為 null');
    //   return false;
    // }
    print('星盤中心: $_chartCenter, 半徑: $_chartRadius');
    print('行星位置記錄數量: ${_planetPositions.length}');

    // 先嘗試直接檢測點擊行星
    if (_planetPositions.isNotEmpty) {
      // 使用我們的自定義方法檢測點擊
      _lastHitPlanet = hitTestPlanet(position);

      // 如果點擊到行星，返回 true
      if (_lastHitPlanet != null) {
        print('直接點擊檢測到行星: ${_lastHitPlanet!.name}');
        return true;
      } else {
        return false;
      }
    } else {
      print('沒有行星位置記錄，無法進行直接點擊檢測');
      print('未找到行星');
      return false;
    }
    return true;
  }

  bool isOverlapping(
      Offset position1, double size1, Offset position2, double size2) {
    return (position1.dx - size1 / 2 < position2.dx + size2 / 2 &&
        position1.dx + size1 / 2 > position2.dx - size2 / 2 &&
        position1.dy - size1 / 2 < position2.dy + size2 / 2 &&
        position1.dy + size1 / 2 > position2.dy - size2 / 2);
  }

  // 識別行星群組，將相近的行星分組
  List<List<PlanetPosition>> _identifyPlanetClusters(
      List<PlanetPosition> planets, double clusterThreshold) {
    final List<List<PlanetPosition>> clusters = [];
    final List<PlanetPosition> sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    if (sortedPlanets.isEmpty) return clusters;

    List<PlanetPosition> currentCluster = [sortedPlanets.first];

    for (int i = 1; i < sortedPlanets.length; i++) {
      final current = sortedPlanets[i];
      final previous = sortedPlanets[i - 1];

      // 計算兩個行星之間的角度差
      double diff = current.longitude - previous.longitude;
      if (diff < 0) diff += 360; // 處理跨越0度的情況

      // 如果角度差小於閾值，將當前行星加入當前群組
      if (diff <= clusterThreshold) {
        currentCluster.add(current);
      } else {
        // 否則，結束當前群組並開始新群組
        clusters.add(List.from(currentCluster));
        currentCluster = [current];
      }
    }

    // 處理最後一個群組
    if (currentCluster.isNotEmpty) {
      clusters.add(currentCluster);
    }

    // 檢查第一個和最後一個群組是否應該合併（處理環形結構）
    if (clusters.length > 1) {
      final firstCluster = clusters.first;
      final lastCluster = clusters.last;

      final firstPlanet = firstCluster.first;
      final lastPlanet = lastCluster.last;

      double diff = firstPlanet.longitude + 360 - lastPlanet.longitude;
      if (diff <= clusterThreshold) {
        // 合併第一個和最後一個群組
        clusters.first = [...lastCluster, ...firstCluster];
        clusters.removeLast();
      }
    }

    return clusters;
  }

  // 計算群組內行星的最佳間距
  Map<String, double> _calculateOptimalPositions(
      List<List<PlanetPosition>> clusters) {
    final Map<String, double> adjustedPositions = {};

    // 處理每個群組
    for (final cluster in clusters) {
      if (cluster.length <= 1) {
        // 單個行星不需要調整
        adjustedPositions[cluster.first.name] = cluster.first.longitude;
        continue;
      }

      // 計算群組的角度範圍
      final firstPlanet = cluster.first;
      final lastPlanet = cluster.last;
      double startAngle = firstPlanet.longitude;
      double endAngle = lastPlanet.longitude;

      // 處理跨越0度的情況
      if (endAngle < startAngle) {
        endAngle += 360;
      }

      // 計算群組的角度範圍
      double rangeAngle = endAngle - startAngle;

      // 計算每個行星之間的最小間距（至少3度）
      double minSpacing = 9.0;
      double requiredSpace = (cluster.length - 1) * minSpacing;

      // 如果群組的角度範圍小於所需空間，擴展範圍
      if (rangeAngle < requiredSpace) {
        // 擴展範圍，使每個行星之間有足夠的空間
        double expansion = requiredSpace - rangeAngle;
        startAngle -= expansion / 2;
        endAngle += expansion / 2;
        rangeAngle = requiredSpace;
      }

      // 計算每個行星之間的間距
      double spacing = rangeAngle / (cluster.length - 1);

      // 分配行星位置
      for (int i = 0; i < cluster.length; i++) {
        double newPosition = startAngle + i * spacing;
        // 確保角度在0-360範圍內
        while (newPosition >= 360) {
          newPosition -= 360;
        }
        while (newPosition < 0) {
          newPosition += 360;
        }

        adjustedPositions[cluster[i].name] = newPosition;
      }
    }

    return adjustedPositions;
  }

  // 繪製行星符號
  @override
  void drawPlanetSymbols(Canvas canvas, Offset center, double radius) {
    final Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.black;

    if (planets.isEmpty) return;

    // 清空行星位置記錄，準備重新記錄
    _planetPositions.clear();

    // 識別行星群組（相距10度以內的行星視為一組）
    final clusters = _identifyPlanetClusters(planets, 30.0);

    // 計算每個行星的最佳位置
    final adjustedPositions = _calculateOptimalPositions(clusters);

    // 按照角度排序行星
    existingPositions.clear();
    final sortedPlanets = planets.toList()
      ..sort((a, b) => a.longitude.compareTo(b.longitude));

    for (final planet in sortedPlanets) {
      // 使用調整後的角度（如果有）
      var angle = adjustedPositions[planet.name] ?? planet.longitude;

      // 繪製行星，半徑為 2 的圓形
      paint.color = planet.color;
      var positionCircle = getPointByAngle(
          center, radius * PLANET_DOT_RADIUS_RATIO, planet.longitude);
      canvas.drawCircle(positionCircle, 2, paint);

      // 使用調整後的角度計算行星符號位置
      var position =
          getPointByAngle(center, radius * PLANET_SYMBOL_RADIUS_RATIO, angle);

      // 繪製行星符號
      final planetTextStyle = TextStyle(
          color: planet.color,
          fontSize: 16,
          fontWeight: FontWeight.normal,
          // fontFeatures: const [FontFeature.disable('liga')],
          // 禁用字型聯合顯示，防止變成 Emoji
          fontFamily: 'astro_one_font');
      final planetPainter = TextPainter(
        text: TextSpan(
          text: planet.symbol,
          style: planetTextStyle,
        ),
        textDirection: TextDirection.ltr,
      );
      planetPainter.layout();

      // 計算行星符號的實際位置
      final symbolPosition = position.translate(
          -planetPainter.width / 2, -planetPainter.height / 2);

      planetPainter.paint(
        canvas,
        symbolPosition,
      );

      // 記錄行星的位置和大小信息，用於點擊檢測
      _planetPositions.add({
        'planet': planet,
        'position': position,
        'width': planetPainter.width * 1.5, // 增加點擊區域
        'height': planetPainter.height * 1.5, // 增加點擊區域
        'angle': angle, // 記錄行星角度
        'longitude': planet.longitude, // 記錄行星經度
      });

      print(
          '記錄行星 ${planet.name} 位置: $position, 角度: $angle, 經度: ${planet.longitude}, 寬度: ${planetPainter.width}, 高度: ${planetPainter.height}');
      var positionLine = getPointByAngle(
          center, radius * PLANET_SYMBOL_RADIUS_RATIO * 0.92, angle);
      // 繪製連接線
      canvas.drawLine(
        positionCircle,
        positionLine,
        Paint()
          ..color = planet.color
          ..strokeWidth = 0.5,
      );

      // 如果行星逆行，繪製逆行標記
      if (planet.longitudeSpeed < 0) {
        var retroPosition = getPointByAngle(
            center, radius * PLANET_SYMBOL_RADIUS_RATIO * 1.15, angle);
        final retroPainter = TextPainter(
          text: const TextSpan(
            text: '℞',
            style: TextStyle(
              color: Colors.red,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        retroPainter.layout();
        retroPainter.paint(
          canvas,
          retroPosition.translate(
              -retroPainter.width / 2, -retroPainter.height / 2),
        );
      }
    }
  }

  // 繪製上升點標記
  void _drawAscendantMarker(Canvas canvas, Offset center, double radius) {
    final ascendantAngle = (_effectiveAscendantDegree);
    // 使用新的 getPointByAngle 方法
    final position = getPointByAngle(
        center, radius * INNER_CIRCLE_RADIUS_RATIO, ascendantAngle);

    // 繪製上升點標記
    final ascendantPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 繪製一個小三角形作為上升點標記
    final path = Path();
    path.moveTo(position.dx, position.dy);
    path.lineTo(position.dx + 10, position.dy + 5);
    path.lineTo(position.dx + 10, position.dy - 5);
    path.close();

    canvas.drawPath(path, ascendantPaint);

    // 繪製上升點文字
    const ascendantTextStyle = TextStyle(
      color: Colors.red,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    final ascendantPainter = TextPainter(
      text: const TextSpan(
        text: 'ASC',
        style: ascendantTextStyle,
      ),
      textDirection: TextDirection.ltr,
    );
    ascendantPainter.layout();
    ascendantPainter.paint(
      canvas,
      position.translate(15, -ascendantPainter.height / 2),
    );
  }

  // 根據角度計算點的位置
  // 參考 Kotlin 函數: getPointByAngle
  @override
  Offset getPointByAngle(Offset pt, double r, double angle) {
    double angleTemp = angle + deltaAngle; // 添加偏移角度
    double x = pt.dx + r * cos(angleTemp * pi / 180); // 轉換為弧度並計算 x 坐標
    double y = pt.dy - r * sin(angleTemp * pi / 180); // 轉換為弧度並計算 y 坐標
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  @override
  double getAspectAngle1(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet1);
    return planet.longitude;
  }

  @override
  double getAspectAngle2(AspectInfo aspect) {
    final planet = planets.firstWhere((p) => p.name == aspect.planet2);
    return planet.longitude;
  }
}
