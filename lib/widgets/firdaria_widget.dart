import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/firdaria_data.dart';
import '../ui/AppTheme.dart';
import '../viewmodels/chart_viewmodel.dart';
import 'styled_card.dart';

/// 法達盤小部件
class FirdariaWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const FirdariaWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<FirdariaWidget> createState() => _FirdariaWidgetState();
}

class _FirdariaWidgetState extends State<FirdariaWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // 檢查是否已經有法達盤數據，如果沒有則計算
    if (widget.viewModel.firdariaData == null || widget.viewModel.firdariaData!.isEmpty) {
      _loadFirdariaData();
    } else {
      // 滾動到當前週期
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentPeriod();
      });
    }
  }

  /// 加載法達盤數據
  Future<void> _loadFirdariaData() async {
    try {
      // 計算法達盤數據
      await widget.viewModel.calculateFirdaria();

      // 數據加載完成後滾動到當前週期
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToCurrentPeriod();
        });
      }
    } catch (e) {
      print('加載法達盤數據時出錯: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 滾動到當前週期
  void _scrollToCurrentPeriod() {
    // 確保有法達盤數據
    if (widget.viewModel.firdariaData == null || widget.viewModel.firdariaData!.isEmpty) return;

    // 確保滾動控制器已附加
    if (!_scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) _scrollToCurrentPeriod();
      });
      return;
    }

    // 找到當前週期的索引
    final currentIndex = widget.viewModel.firdariaData!.indexWhere((period) => period.isCurrent);
    if (currentIndex <= 0) return;

    // 估算位置（每個卡片高度約 200）
    final offset = currentIndex * 200.0;

    // 確保偏移量在有效範圍內
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    final minScrollExtent = _scrollController.position.minScrollExtent;

    if (maxScrollExtent == null || minScrollExtent == null) return;

    final safeOffset = offset.clamp(minScrollExtent, maxScrollExtent);

    try {
      _scrollController.animateTo(
        safeOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      print('滾動錯誤: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 如果正在加載，顯示加載指示器
    if (widget.viewModel.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在計算法達盤數據...')
          ],
        ),
      );
    }

    // 確保有法達盤數據
    if (widget.viewModel.firdariaData == null || widget.viewModel.firdariaData!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('無法計算法達盤數據'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFirdariaData,
              child: const Text('重新計算'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        _buildPersonInfo(),
        _buildCurrentPeriodInfo(),
        Expanded(
          child: _buildPeriodsList(),
        ),
      ],
    );
  }

  /// 構建人物信息
  Widget _buildPersonInfo() {
    final birthData = widget.viewModel.chartData.primaryPerson;
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    return StyledCard(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(
                  birthData.name.isNotEmpty ? birthData.name : '未命名',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(dateFormat.format(birthData.birthDate)),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                Text(birthData.birthPlace),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建當前週期信息
  Widget _buildCurrentPeriodInfo() {
    // 找到當前週期
    final currentPeriod = widget.viewModel.firdariaData!.firstWhere(
      (period) => period.isCurrent,
      orElse: () => widget.viewModel.firdariaData!.first,
    );

    // 找到當前子週期
    final currentSubPeriod = currentPeriod.subPeriods.firstWhere(
      (subPeriod) => subPeriod.isCurrent,
      orElse: () => currentPeriod.subPeriods.first,
    );

    final dateFormat = DateFormat('yyyy-MM-dd');

    return StyledCard(
      margin: const EdgeInsets.all(8),
      color: AppColors.royalIndigo.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '當前週期',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildPlanetIcon(
                  currentPeriod.majorPlanetSymbol,
                  currentPeriod.majorPlanetColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '${currentPeriod.majorPlanetName} 週期',
                  style: const TextStyle(fontSize: 16),
                ),
                const Spacer(),
                Text(
                  '${dateFormat.format(currentPeriod.startDate)} - ${dateFormat.format(currentPeriod.endDate)}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            if (currentSubPeriod != null) ...[
              const Divider(),
              Row(
                children: [
                  _buildPlanetIcon(
                    currentSubPeriod.subPlanetSymbol,
                    currentSubPeriod.subPlanetColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${currentSubPeriod.subPlanetName} 子週期',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const Spacer(),
                  Text(
                    '${dateFormat.format(currentSubPeriod.startDate)} - ${dateFormat.format(currentSubPeriod.endDate)}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建週期列表
  Widget _buildPeriodsList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(8),
      itemCount: widget.viewModel.firdariaData!.length,
      itemBuilder: (context, index) {
        final period = widget.viewModel.firdariaData![index];
        return _buildPeriodCard(period, index);
      },
    );
  }

  /// 構建週期卡片
  Widget _buildPeriodCard(FirdariaData period, int index) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final isSelected = index == widget.viewModel.selectedFirdariaPeriodIndex;

    return StyledCard(
      margin: const EdgeInsets.only(bottom: 8),
      color: period.isCurrent ? AppColors.solarAmber.withOpacity(0.1) : null,
      child: ExpansionTile(
        initiallyExpanded: period.isCurrent || isSelected,
        title: Row(
          children: [
            _buildPlanetIcon(
              period.majorPlanetSymbol,
              period.majorPlanetColor,
            ),
            const SizedBox(width: 8),
            Text(
              '${period.majorPlanetName} 週期',
              style: TextStyle(
                fontWeight: period.isCurrent ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            const Spacer(),
            Text(
              period.durationFormatted,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        subtitle: Text(
          '${dateFormat.format(period.startDate)} - ${dateFormat.format(period.endDate)}',
          style: const TextStyle(fontSize: 12),
        ),
        onExpansionChanged: (expanded) {
          if (expanded) {
            widget.viewModel.selectedFirdariaPeriodIndex = index;
          }
        },
        children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: period.subPeriods.length,
            itemBuilder: (context, subIndex) {
              final subPeriod = period.subPeriods[subIndex];
              return _buildSubPeriodTile(subPeriod);
            },
          ),
        ],
      ),
    );
  }

  /// 構建子週期項目
  Widget _buildSubPeriodTile(FirdariaSubPeriod subPeriod) {
    final dateFormat = DateFormat('yyyy-MM-dd');

    return ListTile(
      leading: _buildPlanetIcon(
        subPeriod.subPlanetSymbol,
        subPeriod.subPlanetColor,
        size: 24,
      ),
      title: Text(
        '${subPeriod.subPlanetName} 子週期',
        style: TextStyle(
          fontSize: 14,
          fontWeight: subPeriod.isCurrent ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        '${dateFormat.format(subPeriod.startDate)} - ${dateFormat.format(subPeriod.endDate)} (${subPeriod.durationFormatted})',
        style: const TextStyle(fontSize: 12),
      ),
      tileColor: subPeriod.isCurrent ? AppColors.solarAmber.withOpacity(0.1) : null,
    );
  }

  /// 構建行星圖標
  Widget _buildPlanetIcon(String symbol, Color color, {double size = 32}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        shape: BoxShape.circle,
        border: Border.all(color: color, width: 1),
      ),
      child: Center(
        child: Text(
          symbol,
          style: TextStyle(
            color: color,
            fontSize: size * 0.6,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
