// 這個文件只在 Web 平台上使用
// 將 dart:html 相關的代碼隔離在這個文件中

import 'dart:convert';
import 'dart:html' as html;

import 'package:package_info_plus/package_info_plus.dart';

import 'logger_utils.dart';

/// 在 Web 平台上觸發 CSV 文件下載
Future<void> triggerWebDownload(String csvContent) async {
  try {
    final bytes = utf8.encode(csvContent);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String appName = packageInfo.appName;
    final fileName = '${appName}_birth_data_${DateTime.now().millisecondsSinceEpoch}.csv';

    html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();

    html.Url.revokeObjectUrl(url);
    logger.i('已在 Web 上觸發 CSV 下載');
  } catch (e) {
    logger.e('Web 下載觸發失敗: $e');
    rethrow;
  }
}
