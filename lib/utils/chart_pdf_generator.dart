import 'dart:typed_data';

import 'package:astreal/utils/zodiac_utils.dart';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:sweph/sweph.dart';

import '../constants/astrology_constants.dart';
import '../models/aspect_info.dart'; // 添加 AspectInfo 模型的引用
import '../models/birth_data.dart';
import '../models/planet_position.dart';
import '../widgets/pdf_preview_screen.dart';
import '../widgets/share_pdf_screen.dart';

/// 星盤數據 PDF 生成器
class ChartPdfGenerator {
  /// 生成星盤數據 PDF
  static Future<Uint8List> generatePdf({
    required BirthData birthData,
    required List<PlanetPosition> planets,
    required List<AspectInfo> aspects,
    required HouseCuspData housesData,
    double? latitude,
    double? longitude,
  }) async {
    try {
      // 創建 PDF 文檔
      final pdf = pw.Document();

      // 獲取中文字體
      final font = await PdfGoogleFonts.notoSansHKRegular();
      final fontBold = await PdfGoogleFonts.notoSansHKBold();

      // 添加頁面
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20), // 減少頁面邊距
          header: (context) => _buildHeader(context, birthData, font, fontBold),
          footer: (context) => _buildFooter(context, font),
          maxPages: 150, // 增加最大頁數限制
          build: (context) => [
            _buildPersonalInfo(birthData, font, fontBold, latitude, longitude),
            pw.SizedBox(height: 10), // 減少間距
            _buildPlanetPositions(planets, font, fontBold),
            pw.SizedBox(height: 10), // 減少間距
            _buildHousesInfo(housesData, planets, font, fontBold),
            pw.SizedBox(height: 10), // 減少間距
            _buildAspectsInfo(aspects, font, fontBold),
          ],
        ),
      );

      // 返回 PDF 文檔的字節數據
      return pdf.save();
    } catch (e) {
      print('生成 PDF 時發生錯誤: $e');
      if (e.toString().contains('TooManyPagesException')) {
        throw Exception('生成的 PDF 頁數超出限制，請減少內容或聯繫開發者。');
      }
      rethrow;
    }
  }

  /// 保存 PDF 到文件並分享
  static Future<void> savePdfAndShare({
    required BuildContext context,
    required Uint8List pdfBytes,
    required String fileName,
  }) async {
    // 使用 SharePdfScreen 小部件
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SharePdfScreen(
          pdfBytes: pdfBytes,
          fileName: fileName,
        ),
      ),
    );
  }

  /// 預覽 PDF
  static Future<void> previewPdf({
    required BuildContext context,
    required Uint8List pdfBytes,
    required String title,
  }) async {
    try {
      // 使用 PdfPreviewScreen 小部件
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PdfPreviewScreen(
            pdfBytes: pdfBytes,
            title: title,
          ),
        ),
      );
    } catch (e) {
      print('預覽 PDF 時發生錯誤: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('預覽 PDF 時發生錯誤: ${e.toString()}'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: '知道了',
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }

  // 構建頁眉
  static pw.Widget _buildHeader(
    pw.Context context,
    BirthData birthData,
    pw.Font font,
    pw.Font fontBold,
  ) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(bottom: 10), // 減少邊距
      child: pw.Text(
        '${birthData.name}的星盤數據',
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 18, // 減小字體大小
          color: PdfColors.blue800,
        ),
      ),
    );
  }

  // 構建頁腳
  static pw.Widget _buildFooter(pw.Context context, pw.Font font) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 5), // 減少邊距
      child: pw.Text(
        '第 ${context.pageNumber} 頁，共 ${context.pagesCount} 頁',
        style: pw.TextStyle(
          font: font,
          fontSize: 8, // 減小字體大小
          color: PdfColors.grey600,
        ),
      ),
    );
  }

  // 構建個人信息部分
  static pw.Widget _buildPersonalInfo(
    BirthData birthData,
    pw.Font font,
    pw.Font fontBold,
    double? latitude,
    double? longitude,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10), // 減少填充
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(6)), // 減小圓角
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            '個人信息',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 14, // 減小字體大小
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          _buildInfoRow('姓名', birthData.name, font, fontBold),
          _buildInfoRow(
              '出生日期', _formatDateTime(birthData.birthDate), font, fontBold),
          _buildInfoRow('出生地點', birthData.birthPlace, font, fontBold),
          if (birthData.notes != null && birthData.notes!.isNotEmpty)
            _buildInfoRow('備註', birthData.notes!, font, fontBold),
          if (latitude != null && longitude != null)
            _buildInfoRow('經緯度', '$latitude, $longitude', font, fontBold),
        ],
      ),
    );
  }

  // 構建行星位置部分
  static pw.Widget _buildPlanetPositions(
    List<PlanetPosition> planets,
    pw.Font font,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10), // 減少填充
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(6)), // 減小圓角
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            '行星位置',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 14, // 減小字體大小
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // 表頭
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.blue50),
                children: [
                  _buildTableCell('行星', font, fontBold, isHeader: true),
                  _buildTableCell('星座', font, fontBold, isHeader: true),
                  _buildTableCell('度數', font, fontBold, isHeader: true),
                  _buildTableCell('宮位', font, fontBold, isHeader: true),
                ],
              ),
              // 行星數據行
              for (final planet in planets)
                pw.TableRow(
                  children: [
                    _buildTableCell(planet.name, font, fontBold),
                    _buildTableCell(planet.sign, font, fontBold),
                    _buildTableCell(
                        _formatDegree(planet.longitude % 30), font, fontBold),
                    _buildTableCell(planet.getHouseText(), font, fontBold),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  // 構建宮位信息部分
  static pw.Widget _buildHousesInfo(
    HouseCuspData housesData,
    List<PlanetPosition> planets,
    pw.Font font,
    pw.Font fontBold,
  ) {
    final List<pw.Widget> houseWidgets = [];

    houseWidgets.add(
      pw.Text(
        '宮位信息',
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 14, // 減小字體大小
          color: PdfColors.blue800,
        ),
      ),
    );

    houseWidgets.add(pw.SizedBox(height: 10));

    // 添加宮位表格
    final List<pw.TableRow> rows = [
      // 表頭
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.blue50),
        children: [
          _buildTableCell('宮位', font, fontBold, isHeader: true),
          _buildTableCell('星座', font, fontBold, isHeader: true),
          _buildTableCell('度數', font, fontBold, isHeader: true),
          _buildTableCell('含義', font, fontBold, isHeader: true),
        ],
      ),
    ];

    // 宮位數據行
    for (int i = 1; i <= 12; i++) {
      final houseAngle = housesData.cusps[i];
      final sign = _getZodiacSign(houseAngle);
      final signDegree = houseAngle % 30;
      final houseDescription = ZodiacUtils.getHouseDescription(i);

      rows.add(
        pw.TableRow(
          children: [
            _buildTableCell('第$i宮', font, fontBold),
            _buildTableCell(sign, font, fontBold),
            _buildTableCell(_formatDegree(signDegree), font, fontBold),
            _buildTableCell(houseDescription, font, fontBold, maxLines: 2),
          ],
        ),
      );
    }

    houseWidgets.add(
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.grey300),
        children: rows,
      ),
    );

    // 添加宮位中的行星信息
    houseWidgets.add(pw.SizedBox(height: 10)); // 減少間距
    houseWidgets.add(
      pw.Text(
        '宮位中的行星',
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 12, // 減小字體大小
          color: PdfColors.blue800,
        ),
      ),
    );
    houseWidgets.add(pw.SizedBox(height: 6)); // 減少間距

    for (int i = 1; i <= 12; i++) {
      final planetsInHouse =
          planets.where((planet) => planet.house == i).toList();
      if (planetsInHouse.isNotEmpty) {
        houseWidgets.add(
          pw.Text(
            '第$i宮:',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 10, // 減小字體大小
            ),
          ),
        );

        for (final planet in planetsInHouse) {
          houseWidgets.add(
            pw.Padding(
              padding: const pw.EdgeInsets.only(left: 10, top: 2, bottom: 2), // 減少填充
              child: pw.Text(
                '${planet.name} (${planet.sign} ${_formatDegree(planet.longitude % 30)})',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 8, // 減小字體大小
                ),
              ),
            ),
          );
        }

        houseWidgets.add(pw.SizedBox(height: 3)); // 減少間距
      }
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(10), // 減少填充
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(6)), // 減小圓角
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: houseWidgets,
      ),
    );
  }

  // 構建相位信息部分
  static pw.Widget _buildAspectsInfo(
    List<AspectInfo> aspects,
    pw.Font font,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10), // 減少填充
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(6)), // 減小圓角
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            '相位信息',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 14, // 減小字體大小
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          if (aspects.isEmpty)
            pw.Text(
              '沒有相位',
              style: pw.TextStyle(
                font: font,
                fontSize: 14,
              ),
            )
          else
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              children: [
                // 表頭
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.blue50),
                  children: [
                    _buildTableCell('行星 1', font, fontBold, isHeader: true),
                    _buildTableCell('相位', font, fontBold, isHeader: true),
                    _buildTableCell('行星 2', font, fontBold, isHeader: true),
                    _buildTableCell('角度', font, fontBold, isHeader: true),
                    _buildTableCell('容許度', font, fontBold, isHeader: true),
                  ],
                ),
                // 相位數據行
                for (final aspect in aspects)
                  pw.TableRow(
                    children: [
                      _buildTableCell(aspect.planet1.name, font, fontBold),
                      _buildTableCell(aspect.aspect, font, fontBold),
                      _buildTableCell(aspect.planet2.name, font, fontBold),
                      _buildTableCell('${aspect.angle}°', font, fontBold),
                      _buildTableCell(
                          '${aspect.orb.toStringAsFixed(2)}°', font, fontBold),
                    ],
                  ),
              ],
            ),
        ],
      ),
    );
  }

  // 構建信息行
  static pw.Widget _buildInfoRow(
    String label,
    String value,
    pw.Font font,
    pw.Font fontBold,
  ) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2), // 減少填充
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 80, // 減少標籤寬度
            child: pw.Text(
              '$label:',
              style: pw.TextStyle(
                font: fontBold,
                fontSize: 10, // 減小字體大小
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: font,
                fontSize: 10, // 減小字體大小
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 構建表格單元格
  static pw.Widget _buildTableCell(
    String text,
    pw.Font font,
    pw.Font fontBold, {
    bool isHeader = false,
    int maxLines = 1,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(horizontal: 4, vertical: 4), // 減少填充
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: isHeader ? fontBold : font,
          fontSize: isHeader ? 10 : 8, // 減小字體大小
        ),
        maxLines: maxLines,
        overflow: pw.TextOverflow.clip,
      ),
    );
  }

  // 將度數轉換為度分秒格式
  static String _formatDegree(double degree) {
    final int deg = degree.floor();
    final double minDouble = (degree - deg) * 60;
    final int min = minDouble.floor();
    final int sec = ((minDouble - min) * 60).round();

    return '$deg°$min\'$sec"';
  }

  // 格式化日期時間
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 獲取星座
  static String _getZodiacSign(double longitude) {
    final int signIndex = (longitude / 30).floor() % 12;
    return AstrologyConstants.ZODIAC_SIGNS[signIndex];
  }
}
