import 'dart:convert';
import 'dart:io' if (dart.library.html) 'dart:io' as io;

import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' as flutter_foundation;
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../models/birth_data.dart';
import 'geocoding_service.dart';
import 'logger_utils.dart';
// 條件導入 web_utils.dart，只在 Web 平台上使用
// ignore: uri_does_not_exist
import 'web_utils.dart' if (dart.library.io) 'web_utils_stub.dart' as web_utils;

final dateFormatter = DateFormat('yyyy-MM-dd HH:mm');

/// CSV 文件處理工具類
/// 提供將出生資料匯出為 CSV 文件和從 CSV 文件匯入出生資料的功能
class CsvHelper {
  // CSV 文件的標題行
  static const List<String> _csvHeaders = [
    'id',
    'name',
    'birthDate',
    'birthPlace',
    'notes',
    'latitude',
    'longitude'
  ];

  // 支持的替代標題行
  static const Map<String, String> _alternativeHeaders = {
    '姓名': 'name',
    '生日': 'birthDate',
    '出生地緯度': 'latitude',
    '出生地經度': 'longitude',
    '出生地': 'birthPlace',
    '居住地緯度': 'residenceLatitude',
    '居住地經度': 'residenceLongitude',
    '標籤': 'tags',
    '是否隱藏': 'isHidden'
  };

  /// 根據平台自動選擇匯出方式
  static Future<String> exportBirthData(List<BirthData> birthDataList) async {
    if (flutter_foundation.kIsWeb) {
      await _exportToCsvWeb(birthDataList);
      return "";
    } else {
      final filePath = await exportBirthDataToCsv(birthDataList);
      return filePath;
    }
  }

  /// 將出生資料列表匯出為 CSV 文件
  ///
  /// [birthDataList] - 要匯出的出生資料列表
  /// 返回生成的 CSV 文件路徑
  static Future<String> exportBirthDataToCsv(
      List<BirthData> birthDataList) async {
    try {
      // 將出生資料轉換為 CSV 格式
      List<List<dynamic>> csvData = [_csvHeaders];

      for (var data in birthDataList) {
        csvData.add([
          data.id,
          data.name,
          dateFormatter.format(data.birthDate),
          data.birthPlace,
          data.notes ?? '',
          data.latitude,
          data.longitude
        ]);
      }

      // 將 CSV 數據轉換為字符串
      String csv = const ListToCsvConverter().convert(csvData);

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String appName = packageInfo.appName;

      // 獲取臨時目錄
      final tempDir = await getTemporaryDirectory();
      final fileName =
          '${appName}_birth_data_${DateTime.now().millisecondsSinceEpoch}.csv';
      final filePath = '${tempDir.path}/$fileName';

      // 寫入文件
      final file = io.File(filePath);
      await file.writeAsString(csv);

      logger.i('成功匯出 ${birthDataList.length} 條出生資料到 CSV 文件: $filePath');
      return filePath;
    } catch (e) {
      logger.e('匯出出生資料到 CSV 文件時出錯: $e');
      rethrow;
    }
  }

  /// Web 平台：觸發瀏覽器下載
  static Future<void> _exportToCsvWeb(List<BirthData> birthDataList) async {
    try {
      List<List<dynamic>> csvData = [_csvHeaders];

      for (var data in birthDataList) {
        csvData.add([
          data.id,
          data.name,
          dateFormatter.format(data.birthDate),
          data.birthPlace,
          data.notes ?? '',
          data.latitude,
          data.longitude
        ]);
      }

      String csv = const ListToCsvConverter().convert(csvData);

      // 使用條件導入來處理 Web 平台
      if (flutter_foundation.kIsWeb) {
        // 使用條件導入的 web_utils
        await web_utils.triggerWebDownload(csv);
      } else {
        // 非 Web 平台不應該執行到這裡
        logger.w('非 Web 平台不應該調用 _exportToCsvWeb 方法');
      }

      logger.i('已在 Web 上觸發 CSV 下載');
    } catch (e) {
      logger.e('Web 匯出 CSV 時出錯: $e');
    }
  }

  /// 從 CSV 文件匯入出生資料
  ///
  /// 返回匯入的出生資料列表
  static Future<List<BirthData>> importBirthDataFromCsv() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        logger.w('未選擇 CSV 文件');
        return [];
      }

      String? csvString;
      if (result.files.single.bytes != null) {
        csvString = utf8.decode(result.files.single.bytes!);
      } else if (result.files.single.path != null && !flutter_foundation.kIsWeb) {
        final file = io.File(result.files.single.path!);
        csvString = await file.readAsString();
      } else {
        throw Exception('無法讀取選取的 CSV 文件');
      }

      logger.d('原始 CSV 內容:\n$csvString');
      List<List<dynamic>> csvData = const CsvToListConverter(
        fieldDelimiter: ',',
        eol: '\n',
        shouldParseNumbers: false, // 避免把 0.0、false 亂轉型
      ).convert(csvString);

      if (csvData.isEmpty) {
        logger.w('CSV 文件為空');
        return [];
      }

      final originalHeaders = csvData[0].map((e) => e.toString()).toList();
      Map<int, String> headerMap = {};
      for (int i = 0; i < originalHeaders.length; i++) {
        String header = originalHeaders[i];
        if (_alternativeHeaders.containsKey(header)) {
          headerMap[i] = _alternativeHeaders[header]!;
        } else {
          headerMap[i] = header;
        }
      }

      final mappedHeaders = headerMap.values.toList();
      bool hasNameColumn = mappedHeaders.contains('name');
      bool hasBirthPlaceColumn = mappedHeaders.contains('birthPlace');

      if (!hasNameColumn || !hasBirthPlaceColumn) {
        throw Exception('CSV 文件缺少必要的列：${!hasNameColumn ? "姓名" : ""} ${!hasBirthPlaceColumn ? "出生地" : ""}');
      }

      List<BirthData> importedData = [];

      for (int i = 1; i < csvData.length; i++) {
        List<dynamic> row = csvData[i];

        if (row.length < originalHeaders.length) {
          logger.w('行 $i 欄位數不足，將嘗試修正...');
          String rowStr = row.join(',');
          List<String> splitRow = _fixRow(rowStr);
          row = splitRow.map((e) => e.trim()).toList();
        }

        if (row.isEmpty || row.every((cell) => cell == null || cell.toString().trim().isEmpty)) {
          logger.d('跳過空行: $i');
          continue;
        }

        Map<String, dynamic> rowMap = {};
        for (int j = 0; j < row.length && j < originalHeaders.length; j++) {
          String mappedHeader = headerMap[j] ?? originalHeaders[j];
          rowMap[mappedHeader] = row[j];
        }

        if (rowMap['name'] == null || rowMap['birthPlace'] == null) {
          logger.w('跳過缺少必要欄位的行 $i: $rowMap');
          continue;
        }

        double? latitude = _tryParseDouble(rowMap['latitude']);
        double? longitude = _tryParseDouble(rowMap['longitude']);

        if (_isInvalidCoord(latitude) || _isInvalidCoord(longitude)) {
          try {
            final coordinates = await GeocodingService.getCoordinatesFromAddress(rowMap['birthPlace'].toString());
            latitude = coordinates['latitude'];
            longitude = coordinates['longitude'];
            logger.d('地址轉換成功: ${rowMap['birthPlace']} -> $latitude, $longitude');
          } catch (e) {
            logger.e('地址轉換失敗: ${rowMap['birthPlace']}, 錯誤: $e');
            continue;
          }
        }

        DateTime? birthDate;
        String? birthDateStr = rowMap['birthDate']?.toString().trim();
        if (birthDateStr != null && birthDateStr.isNotEmpty) {
          try {
            birthDate = DateTime.parse(birthDateStr);
          } catch (_) {
            try {
              birthDate = DateFormat('yyyy/MM/dd HH:mm').parseStrict(birthDateStr);
              logger.d('使用備用格式解析成功: $birthDateStr -> $birthDate');
            } catch (e) {
              logger.w('解析日期失敗 (跳過): $birthDateStr, 錯誤: $e');
              continue;
            }
          }
        }

        if (birthDate == null || latitude == null || longitude == null) {
          logger.w('資料不完整，跳過第 $i 行: birthDate=$birthDate, lat=$latitude, lon=$longitude');
          continue;
        }

        try {
          final birthData = BirthData(
            id: rowMap['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
            name: rowMap['name'].toString(),
            birthDate: birthDate,
            birthPlace: rowMap['birthPlace'].toString(),
            notes: rowMap['tags']?.toString() ?? rowMap['notes']?.toString(),
            latitude: latitude,
            longitude: longitude,
          );

          importedData.add(birthData);
          logger.d('成功匯入: ${birthData.name}, ${birthData.birthDate}, ${birthData.birthPlace}');
        } catch (e) {
          logger.e('建立 BirthData 時出錯 (第 $i 行): $row, 錯誤: $e');
        }
      }

      logger.i('共匯入 ${importedData.length} 筆出生資料');
      return importedData;
    } catch (e) {
      logger.e('匯入過程發生錯誤: $e');
      rethrow;
    }
  }

  static double? _tryParseDouble(dynamic value) {
    if (value == null) return null;
    return double.tryParse(value.toString());
  }

  static bool _isInvalidCoord(double? coord) {
    return coord == null || coord == -1.0;
  }


// 修正錯位的資料，主要是將帶逗號的欄位合併
  static List<String> _fixRow(String rowStr) {
    List<String> columns = [];
    bool insideQuotes = false;
    StringBuffer currentColumn = StringBuffer();

    for (int i = 0; i < rowStr.length; i++) {
      String char = rowStr[i];

      if (char == ',' && !insideQuotes) {
        columns.add(currentColumn.toString().trim());
        currentColumn.clear();
      } else if (char == '"') {
        insideQuotes = !insideQuotes; // 進入或退出引號內部
      } else {
        currentColumn.write(char);
      }
    }
    // 最後一個欄位
    columns.add(currentColumn.toString().trim());

    return columns;
  }

  /// 分享 CSV 文件
  ///
  /// [filePath] - CSV 文件路徑
  static Future<void> shareCsvFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)], text: '出生資料 CSV 文件');
      logger.i('已分享 CSV 文件: $filePath');
    } catch (e) {
      logger.e('分享 CSV 文件時出錯: $e');
      rethrow;
    }
  }

  /// 解析單行 CSV 數據
  ///
  /// 專門處理格式為：姓名,生日,出生地緯度,出生地經度,出生地,居住地緯度,居住地經度,標籤,是否隱藏
  /// 的 CSV 數據
  static Future<List<BirthData>> parseCSV(List<List<dynamic>> csvData) async {
    final List<BirthData> birthDataList = [];

    try {
      if (csvData.isEmpty) {
        logger.w('CSV 文件為空');
        return [];
      }

      final originalHeaders = csvData[0].map((e) => e.toString()).toList();
      logger.d('原始 CSV 標題: $originalHeaders');

      // 標題映射：原始標題 -> 標準化標題
      final headers = List.generate(
        originalHeaders.length,
            (i) => _alternativeHeaders[originalHeaders[i]] ?? originalHeaders[i],
      );

      for (int rowIndex = 1; rowIndex < csvData.length; rowIndex++) {
        final row = csvData[rowIndex];
        if (row.length != headers.length) {
          logger.w('第 $rowIndex 行的欄位數與標題不一致，略過。');
          continue;
        }

        final Map<String, String> standardDataMap = {};
        for (int i = 0; i < headers.length; i++) {
          standardDataMap[headers[i]] = row[i]?.toString() ?? '';
        }

        // 特殊處理：補足常見欄位
        if (!standardDataMap.containsKey('birthPlace') &&
            standardDataMap.containsKey('出生地')) {
          standardDataMap['birthPlace'] = standardDataMap['出生地']!;
        }

        if (!standardDataMap.containsKey('name') &&
            standardDataMap.containsKey('姓名')) {
          standardDataMap['name'] = standardDataMap['姓名']!;
        }

        if (!standardDataMap.containsKey('birthDate') &&
            standardDataMap.containsKey('生日')) {
          standardDataMap['birthDate'] = standardDataMap['生日']!;
        }

        // 如果沒有名字或出生地，這筆就真的不成立了
        if (!standardDataMap.containsKey('name') ||
            !standardDataMap.containsKey('birthPlace')) {
          logger.w('第 $rowIndex 筆缺少必要欄位 name 或 birthPlace，略過。');
          continue;
        }

        // 經緯度處理
        double? latitude;
        double? longitude;

        latitude = double.tryParse(standardDataMap['latitude'] ?? '');
        longitude = double.tryParse(standardDataMap['longitude'] ?? '');

        if ((latitude == null || longitude == null) ||
            latitude == -1.0 || longitude == -1.0) {
          if (standardDataMap.containsKey('出生地緯度') &&
              standardDataMap.containsKey('出生地經度')) {
            latitude = double.tryParse(standardDataMap['出生地緯度'] ?? '');
            longitude = double.tryParse(standardDataMap['出生地經度'] ?? '');
          }
        }

        if ((latitude == null || longitude == null) ||
            latitude == -1.0 || longitude == -1.0) {
          try {
            final coordinates =
            await GeocodingService.getCoordinatesFromAddress(
                standardDataMap['birthPlace']!);
            if (coordinates != null &&
                coordinates['latitude'] != null &&
                coordinates['longitude'] != null) {
              latitude = coordinates['latitude'];
              longitude = coordinates['longitude'];
              logger.d(
                  '從地址取得經緯度成功: ${standardDataMap['birthPlace']} -> $latitude, $longitude');
            } else {
              logger.w('無法從地址取得經緯度，使用預設: ${standardDataMap['birthPlace']}');
              latitude = 0.0;
              longitude = 0.0;
            }
          } catch (e) {
            logger.e('第 $rowIndex 筆地點解析錯誤: $e');
            latitude = 0.0;
            longitude = 0.0;
          }
        }

        // 解析生日
        DateTime birthDate;
        String? birthDateStr = standardDataMap['birthDate'];

        if (birthDateStr == null || birthDateStr.trim().isEmpty) {
          logger.w('第 $rowIndex 筆缺生日，使用預設日期');
          birthDate = DateTime(1900, 1, 1);
          standardDataMap['notes'] = '生日待補';
        } else {
          try {
            if (birthDateStr.contains('/')) {
              List<String> parts = birthDateStr.split(' ');
              String datePart = parts[0];
              String timePart = parts.length > 1 ? parts[1] : '00:00';

              List<String> dateParts = datePart.split('/');
              List<String> timeParts = timePart.split(':');

              int year = int.parse(dateParts[0]);
              int month = int.parse(dateParts[1]);
              int day = int.parse(dateParts[2]);
              int hour = int.parse(timeParts[0]);
              int minute = int.parse(timeParts[1]);

              birthDate = DateTime(year, month, day, hour, minute);
            } else {
              birthDate = DateTime.parse(birthDateStr);
            }
          } catch (e) {
            logger.e('第 $rowIndex 筆生日解析錯誤: $e');
            birthDate = DateTime(1900, 1, 1);
            standardDataMap['notes'] = '生日格式錯誤';
          }
        }

        final birthData = BirthData(
          id: standardDataMap['id'] ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: standardDataMap['name']!,
          birthDate: birthDate,
          birthPlace: standardDataMap['birthPlace']!,
          latitude: latitude!,
          longitude: longitude!,
          notes: [
            standardDataMap['tags'],
            standardDataMap['notes']
          ].whereType<String>().where((s) => s.trim().isNotEmpty).join(' / '),
        );

        logger.d(
            '匯入成功: ${birthData.name}, ${birthData.birthDate}, ${birthData.birthPlace}');
        birthDataList.add(birthData);
      }

      return birthDataList;
    } catch (e) {
      logger.e('整體 CSV 解析失敗: $e');
      return [];
    }
  }
}
