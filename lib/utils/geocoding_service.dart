import 'dart:convert';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

import 'LoggerUtils.dart';

/// 地理編碼服務
/// 提供將地址轉換為經緯度的功能
class GeocodingService {
  /// 使用 Flutter 的 geocoding 套件進行地理編碼
  /// 將地址轉換為經緯度
  static Future<Map<String, double>> getCoordinatesFromAddress(
      String address) async {
    try {
      // 使用 geocoding 套件進行地理編碼
      List<Location> locations = await locationFromAddress(address);

      if (locations.isNotEmpty) {
        Location location = locations.first;
        logger.d(
            '地址 "$address" 的經緯度: ${location.latitude}, ${location.longitude}');
        return {
          'latitude': location.latitude,
          'longitude': location.longitude,
        };
      } else {
        logger.w('找不到地址 "$address" 的經緯度');
        // 如果 geocoding 套件無法解析地址，嘗試使用 OpenStreetMap Nominatim API
        return await _getCoordinatesFromNominatim(address);
      }
    } catch (e) {
      logger.e('使用 geocoding 套件進行地理編碼時出錯: $e');
      // 如果 geocoding 套件出錯，嘗試使用 OpenStreetMap Nominatim API
      return await _getCoordinatesFromNominatim(address);
    }
  }

  /// 使用 OpenStreetMap Nominatim API 進行地理編碼
  /// 作為備用方案
  static Future<Map<String, double>> _getCoordinatesFromNominatim(
      String address) async {
    try {
      // 對地址進行 URL 編碼
      final encodedAddress = Uri.encodeComponent(address);

      // 構建 Nominatim API 請求 URL
      final url =
          'https://nominatim.openstreetmap.org/search?q=$encodedAddress&format=json&limit=1';

      // 發送 HTTP 請求
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'AstroMatch/1.0', // Nominatim 要求提供 User-Agent
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        if (data.isNotEmpty) {
          final double latitude = double.parse(data[0]['lat']);
          final double longitude = double.parse(data[0]['lon']);

          logger.d(
              '使用 Nominatim API 獲取地址 "$address" 的經緯度: $latitude, $longitude');

          return {
            'latitude': latitude,
            'longitude': longitude,
          };
        } else {
          logger.w('Nominatim API 找不到地址 "$address" 的經緯度');
          return getDefaultCoordinates();
        }
      } else {
        logger.e('Nominatim API 請求失敗: ${response.statusCode}');
        return getDefaultCoordinates();
      }
    } catch (e) {
      logger.e('使用 Nominatim API 進行地理編碼時出錯: $e');
      return getDefaultCoordinates();
    }
  }

  /// 獲取預設的台北經緯度
  static Map<String, double> getDefaultCoordinates() {
    return {
      'latitude': 25.03, // 台北緯度
      'longitude': 121.57, // 台北經度
    };
  }

  /// 獲取當前位置的經緯度
  static Future<Map<String, double>?> getCurrentLocation() async {
    try {
      // 檢查位置服務是否啟用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        logger.w('位置服務未啟用');
        return null;
      }

      // 檢查位置權限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          logger.w('位置權限被拒絕');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        logger.w('位置權限被永久拒絕');
        return null;
      }

      // 獲取當前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      logger.d('獲取當前位置: ${position.latitude}, ${position.longitude}');

      return {
        'latitude': position.latitude,
        'longitude': position.longitude,
      };
    } catch (e) {
      logger.e('獲取當前位置時出錯: $e');
      return null;
    }
  }

  /// 根據經緯度獲取地址
  static Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // 如果是 Web 環境，使用 Nominatim API 獲取地址
      if (kIsWeb) {
        return await _getAddressFromNominatimAPI(latitude, longitude);
      }

      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;

        // 構建地址字符串
        String address = '';

        // 台灣地址格式
        if (place.country == 'Taiwan') {
          if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
            address += place.administrativeArea!; // 縣/市
          }
          if (place.locality != null && place.locality!.isNotEmpty) {
            address += place.locality!; // 鄉鎮市區
          }
        } else {
          // 其他國家的地址格式
          if (place.locality != null && place.locality!.isNotEmpty) {
            address += place.locality!;
          }
          if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
            if (address.isNotEmpty) address += ', ';
            address += place.administrativeArea!;
          }
          if (place.country != null && place.country!.isNotEmpty) {
            if (address.isNotEmpty) address += ', ';
            address += place.country!;
          }
        }

        logger.d('經緯度 $latitude, $longitude 的地址: $address');
        return address;
      } else {
        logger.w('找不到經緯度 $latitude, $longitude 的地址');
        // 如果本地獲取失敗，嘗試使用 Nominatim API
        return await _getAddressFromNominatimAPI(latitude, longitude);
      }
    } catch (e) {
      logger.e('根據經緯度獲取地址時出錯: $e');
      // 如果出錯，嘗試使用 Nominatim API
      return await _getAddressFromNominatimAPI(latitude, longitude);
    }
  }

  /// 使用 Nominatim API 獲取地址
  static Future<String?> _getAddressFromNominatimAPI(double latitude, double longitude) async {
    try {
      // 構建 Nominatim API 請求 URL
      final url = 'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1';

      // 發送 HTTP 請求
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'AstroMatch/1.0', // Nominatim 要求提供 User-Agent
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data != null && data['display_name'] != null) {
          String address = data['display_name'];

          // 如果有詳細地址信息，嘗試提取縣市和區域
          if (data['address'] != null) {
            String simpleAddress = '';

            // 先嘗試取台灣的縣市和區域
            if (data['address']['city'] != null) {
              simpleAddress = data['address']['city'];
            } else if (data['address']['county'] != null) {
              simpleAddress = data['address']['county'];
            } else if (data['address']['state'] != null) {
              simpleAddress = data['address']['state'];
            }

            // 如果有區域信息，添加到地址中
            if (data['address']['suburb'] != null && simpleAddress.isNotEmpty) {
              simpleAddress += data['address']['suburb'];
            } else if (data['address']['district'] != null && simpleAddress.isNotEmpty) {
              simpleAddress += data['address']['district'];
            }

            // 如果成功提取了簡化地址，則使用簡化地址
            if (simpleAddress.isNotEmpty) {
              address = simpleAddress;
            }
          }

          logger.d('使用 Nominatim API 獲取地址: $address');
          return address;
        }
      }

      logger.w('Nominatim API 獲取地址失敗: ${response.statusCode}');
      return null;
    } catch (e) {
      logger.e('使用 Nominatim API 獲取地址時出錯: $e');
      return null;
    }
  }
}
