// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBCTw5KZQC10-xYmciTb6VLdYkCkrfH6m0',
    appId: '1:470077449550:web:18e235ee5adb571396aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    authDomain: 'astreal-d3f70.firebaseapp.com',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    measurementId: 'G-WC18X2QD4Z',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAeCMo-vuea1Z6nS1_EwOygN8TOY3ncmMc',
    appId: '1:470077449550:android:4971c9e15686127296aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBD-jSb1JUoJExk8x-f63Z-2iKmxXS8UB8',
    appId: '1:470077449550:ios:dcbc192966cde49096aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    iosBundleId: 'com.one.astreal',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBD-jSb1JUoJExk8x-f63Z-2iKmxXS8UB8',
    appId: '1:470077449550:ios:e2a7e2f46dde27c696aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    iosBundleId: 'com.one.astreal.astreal',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBCTw5KZQC10-xYmciTb6VLdYkCkrfH6m0',
    appId: '1:470077449550:web:cd69183425ecccc596aa1f',
    messagingSenderId: '470077449550',
    projectId: 'astreal-d3f70',
    authDomain: 'astreal-d3f70.firebaseapp.com',
    storageBucket: 'astreal-d3f70.firebasestorage.app',
    measurementId: 'G-LCDSZLYTEG',
  );
}
