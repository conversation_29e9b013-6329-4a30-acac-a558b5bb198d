import 'package:flutter/material.dart';

class AppColors {
  // 主要顏色
  static const Color royalIndigo = Color(0xFF3F51B5);  // 主色：皇家靛藍
  static const Color solarAmber = Color(0xFFF5A623);   // 點綴：流光黃
  static const Color indigoSurface = Color(0xFF303F9F); // 較深靛藍，用於 AppBar 或底部
  static const Color indigoLight = Color(0xFF7986CB);   // 輕靛藍，用於強調元素

  // 輔助顏色
  static const Color paleAmber = Color(0xFFFFE9B3);     // 柔和黃，背景可用
  static const Color softGray = Color(0xFFEEEEEE);      // 卡片或背景底色
  static const Color textDark = Color(0xFF212121);      // 深色文字
  static const Color textMedium = Color(0xFF757575);    // 中灰色文字
  static const Color textLight = Color(0xFFBDBDBD);     // 淺灰色文字

  // 功能顏色
  static const Color success = Color(0xFF4CAF50);       // 成功綠
  static const Color warning = Color(0xFFFF9800);       // 警告橙
  static const Color error = Color(0xFFE53935);         // 錯誤紅
  static const Color info = Color(0xFF2196F3);          // 信息藍

  // 背景與卡片
  static const Color cardBackground = Colors.white;      // 卡片背景
  static const Color scaffoldBackground = Color(0xFFF5F5F5); // 主背景色
}

class AstrealAppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      scaffoldBackgroundColor: AppColors.scaffoldBackground,
      primaryColor: AppColors.royalIndigo,
      useMaterial3: true, // 啟用 Material 3
      colorScheme: const ColorScheme.light(
        primary: AppColors.royalIndigo,
        secondary: AppColors.solarAmber,
        tertiary: AppColors.indigoLight,
        background: AppColors.scaffoldBackground,
        surface: AppColors.softGray,
        onPrimary: Colors.white,
        onSecondary: Colors.black,
        error: AppColors.error,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.indigoSurface,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),
      // 添加 NavigationBar 主題
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: Colors.white,
        indicatorColor: AppColors.royalIndigo.withOpacity(0.1),
        labelTextStyle: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return const TextStyle(
              color: AppColors.royalIndigo,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            );
          }
          return const TextStyle(
            color: AppColors.textMedium,
            fontSize: 12,
          );
        }),
        iconTheme: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return const IconThemeData(
              color: AppColors.royalIndigo,
              size: 24,
            );
          }
          return const IconThemeData(
            color: AppColors.textMedium,
            size: 24,
          );
        }),
        elevation: 2,
        height: 65,
        shadowColor: Colors.black.withOpacity(0.1),
      ),
      cardTheme: CardTheme(
        color: AppColors.cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withOpacity(0.1),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: AppColors.textDark, fontSize: 26, fontWeight: FontWeight.bold),
        displayMedium: TextStyle(color: AppColors.textDark, fontSize: 22, fontWeight: FontWeight.bold),
        displaySmall: TextStyle(color: AppColors.textDark, fontSize: 18, fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(color: AppColors.textDark, fontSize: 16),
        bodyMedium: TextStyle(color: AppColors.textDark, fontSize: 14),
        bodySmall: TextStyle(color: AppColors.textMedium, fontSize: 12),
        titleLarge: TextStyle(color: AppColors.royalIndigo, fontSize: 20, fontWeight: FontWeight.bold),
        titleMedium: TextStyle(color: AppColors.royalIndigo, fontSize: 18, fontWeight: FontWeight.bold),
        titleSmall: TextStyle(color: AppColors.royalIndigo, fontSize: 16, fontWeight: FontWeight.bold),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.textLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.textLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.royalIndigo, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.royalIndigo,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}