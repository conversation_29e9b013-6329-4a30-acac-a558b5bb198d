import 'package:astreal/models/birth_data.dart';
import 'package:astreal/services/equinox_solstice_service.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:astreal/widgets/styled_card.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'chart_page.dart';
import 'location_picker_page.dart';

/// 二分二至圖頁面
///
/// 顯示春分、夏至、秋分、冬至四個節氣的時間和星盤
class EquinoxSolsticePage extends StatefulWidget {
  final BirthData? natalPerson; // 本命盤人物，用於比較
  final BirthData? location; // 觀測地點

  const EquinoxSolsticePage({
    super.key,
    this.natalPerson,
    this.location,
  });

  @override
  State<EquinoxSolsticePage> createState() => _EquinoxSolsticePageState();
}

class _EquinoxSolsticePageState extends State<EquinoxSolsticePage> {
  final EquinoxSolsticeService _service = EquinoxSolsticeService();
  List<SeasonData> _seasons = [];
  bool _isLoading = true;
  String? _error;
  int _selectedYear = DateTime.now().year;

  // 新增的選擇器狀態
  BirthData? _selectedLocation;
  SeasonType? _selectedSeasonType; // null 表示顯示全部，非 null 表示只顯示特定節氣

  @override
  void initState() {
    super.initState();
    // 初始化選中的地點
    _selectedLocation = widget.location ?? BirthData(
      id: 'default_location',
      name: '台北',
      birthDate: DateTime.now(),
      latitude: 25.0330,
      longitude: 121.5654,
      birthPlace: '台北',
    );
    _loadSeasons();
  }

  /// 載入季節節氣數據
  Future<void> _loadSeasons() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final seasons = await _service.calculateSeasonTimes(
        _selectedYear,
        latitude: _selectedLocation!.latitude,
        longitude: _selectedLocation!.longitude,
      );

      final seasonsWithCharts = await _service.generateSeasonCharts(
        seasons,
        _selectedLocation!,
        natalPerson: widget.natalPerson,
      );

      setState(() {
        _seasons = seasonsWithCharts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = '載入季節節氣數據失敗: $e';
        _isLoading = false;
      });
    }
  }

  /// 選擇年份
  Future<void> _selectYear() async {
    final selectedYear = await showDialog<int>(
      context: context,
      builder: (context) => _YearPickerDialog(
        initialYear: _selectedYear,
        minYear: 1900,
        maxYear: 2100,
      ),
    );

    if (selectedYear != null && selectedYear != _selectedYear) {
      setState(() {
        _selectedYear = selectedYear;
      });
      _loadSeasons();
    }
  }

  /// 選擇地點
  Future<void> _selectLocation() async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => const LocationPickerPage(),
      ),
    );

    if (result != null) {
      final selectedLocation = BirthData(
        id: 'selected_location',
        name: result['name'],
        birthDate: DateTime.now(),
        latitude: result['latitude'],
        longitude: result['longitude'],
        birthPlace: result['name'],
      );

      setState(() {
        _selectedLocation = selectedLocation;
      });
      _loadSeasons();
    }
  }

  /// 選擇節氣類型
  Future<void> _selectSeasonType() async {
    final selectedType = await showDialog<SeasonType?>(
      context: context,
      builder: (context) => _SeasonTypePickerDialog(
        selectedType: _selectedSeasonType,
      ),
    );

    if (selectedType != _selectedSeasonType) {
      setState(() {
        _selectedSeasonType = selectedType;
      });
    }
  }

  /// 進入星盤頁面
  void _enterChartPage(SeasonData season) {
    if (season.chartData == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(
            initialChartData: season.chartData!,
          ),
          child: ChartPage(chartData: season.chartData!),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String title = '$_selectedYear年 二分二至圖';
    if (_selectedSeasonType != null) {
      title = '$_selectedYear年 ${_selectedSeasonType!.displayName}';
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: [
          // 年份選擇
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectYear,
            tooltip: '選擇年份',
          ),
          // 地點選擇
          IconButton(
            icon: const Icon(Icons.location_on),
            onPressed: _selectLocation,
            tooltip: '選擇地點',
          ),
          // 節氣類型選擇
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _selectSeasonType,
            tooltip: '選擇節氣',
          ),
          // 重新載入
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSeasons,
            tooltip: '重新載入',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在計算季節節氣時間...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSeasons,
              child: const Text('重試'),
            ),
          ],
        ),
      );
    }

    // 篩選季節數據
    final filteredSeasons = _selectedSeasonType == null
        ? _seasons
        : _seasons.where((season) => season.seasonType == _selectedSeasonType).toList();

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 選擇器狀態卡片
        _buildSelectionStatusCard(),

        // 年份概覽卡片（只在顯示全部時顯示）
        if (_selectedSeasonType == null) ...[
          _buildYearOverviewCard(),
        ],

        // 季節節氣列表
        if (filteredSeasons.isNotEmpty) ...[
          ...filteredSeasons.map((season) => _buildSeasonCard(season)),
        ] else ...[
          _buildEmptyStateCard(),
        ],

        // 說明文字
        _buildDescriptionCard(),
        const SizedBox(height: 16),
      ],
    );
  }

  /// 構建選擇器狀態卡片
  Widget _buildSelectionStatusCard() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CardTitle(
            title: '當前設定',
            icon: Icons.settings,
            iconColor: AppColors.royalIndigo,
          ),
          const SizedBox(height: 12),

          // 年份
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text('年份：$_selectedYear年', style: const TextStyle(fontSize: 14)),
            ],
          ),
          const SizedBox(height: 8),

          // 地點
          Row(
            children: [
              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '地點：${_selectedLocation?.name ?? "未設定"}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 節氣類型
          Row(
            children: [
              Icon(Icons.filter_list, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '節氣：${_selectedSeasonType?.displayName ?? "顯示全部"}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建空狀態卡片
  Widget _buildEmptyStateCard() {
    return StyledCard(
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '沒有找到符合條件的節氣資料',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '請嘗試調整篩選條件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建年份概覽卡片
  Widget _buildYearOverviewCard() {
    final now = DateTime.now();
    final nextSeason = _getNextSeason();

    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CardTitle(
            title: '$_selectedYear年 四季節氣',
            icon: Icons.wb_sunny,
            iconColor: AppColors.solarAmber,
          ),
          const SizedBox(height: 12),

          // 基本說明
          Text(
            '顯示春分、夏至、秋分、冬至四個重要節氣的精確時間和星盤配置。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),

          // 下一個節氣提示
          if (nextSeason != null && _selectedYear == now.year) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getSeasonColor(nextSeason.seasonType).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getSeasonColor(nextSeason.seasonType).withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getSeasonIcon(nextSeason.seasonType),
                    color: _getSeasonColor(nextSeason.seasonType),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '下一個節氣：${nextSeason.seasonType.displayName}',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: _getSeasonColor(nextSeason.seasonType),
                          ),
                        ),
                        Text(
                          _formatDateTime(nextSeason.dateTime),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    '${nextSeason.dateTime.difference(now).inDays}天後',
                    style: TextStyle(
                      fontSize: 12,
                      color: _getSeasonColor(nextSeason.seasonType),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // 本命盤比較說明
          if (widget.natalPerson != null) ...[
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () => _enterNextSeasonComparisonChart(),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.royalIndigo.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.person,
                      color: AppColors.royalIndigo,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '與 ${widget.natalPerson!.name} 的本命盤進行比較分析',
                            style: const TextStyle(
                              fontSize: 13,
                              color: AppColors.royalIndigo,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (nextSeason != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              '點擊查看與${nextSeason.seasonType.displayName}的比較盤',
                              style: TextStyle(
                                fontSize: 11,
                                color: AppColors.royalIndigo.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: AppColors.royalIndigo.withOpacity(0.7),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建季節卡片
  Widget _buildSeasonCard(SeasonData season) {
    final seasonIcon = _getSeasonIcon(season.seasonType);
    final seasonColor = _getSeasonColor(season.seasonType);
    final now = DateTime.now();
    final isUpcoming = season.dateTime.isAfter(now);
    final isPast = season.dateTime.isBefore(now);
    final daysDifference = season.dateTime.difference(now).inDays;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: StyledCard(
        onTap: season.chartData != null ? () => _enterChartPage(season) : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                seasonColor.withOpacity(0.1),
                seasonColor.withOpacity(0.05),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 季節圖標和狀態指示器
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: seasonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    seasonIcon,
                    color: seasonColor,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),

                // 主要信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            season.seasonType.displayName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (isUpcoming && daysDifference <= 30)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: seasonColor.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '即將到來',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: seasonColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDateTime(season.dateTime),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSeasonShortDescription(season.seasonType),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[700],
                          height: 1.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // 狀態和導航
                Column(
                  children: [
                    if (isUpcoming)
                      Text(
                        '${daysDifference.abs()}天後',
                        style: TextStyle(
                          fontSize: 12,
                          color: seasonColor,
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    else if (isPast)
                      Text(
                        '${daysDifference.abs()}天前',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    const SizedBox(height: 4),
                    if (season.chartData != null)
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 獲取季節簡短描述
  String _getSeasonShortDescription(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return '太陽進入白羊座，新的開始與成長';
      case SeasonType.summerSolstice:
        return '太陽進入巨蟹座，陽氣最盛';
      case SeasonType.autumnEquinox:
        return '太陽進入天秤座，平衡與收穫';
      case SeasonType.winterSolstice:
        return '太陽進入摩羯座，陰氣最盛';
    }
  }

  /// 構建季節描述
  Widget _buildSeasonDescription(SeasonType seasonType) {
    String description;
    switch (seasonType) {
      case SeasonType.springEquinox:
        description = '太陽進入白羊座，晝夜等長，象徵新的開始與成長。';
        break;
      case SeasonType.summerSolstice:
        description = '太陽進入巨蟹座，北半球白晝最長，陽氣最盛。';
        break;
      case SeasonType.autumnEquinox:
        description = '太陽進入天秤座，晝夜等長，象徵平衡與收穫。';
        break;
      case SeasonType.winterSolstice:
        description = '太陽進入摩羯座，北半球白晝最短，陰氣最盛。';
        break;
    }

    return Text(
      description,
      style: TextStyle(
        fontSize: 13,
        color: Colors.grey[700],
        height: 1.3,
      ),
    );
  }

  /// 構建說明卡片
  Widget _buildDescriptionCard() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CardTitle(
            title: '關於二分二至圖',
            icon: Icons.info_outline,
            iconColor: AppColors.royalIndigo,
          ),
          const SizedBox(height: 12),
          const Text(
            '二分二至圖顯示一年中四個重要的天文節點：',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          _buildInfoItem('春分', '太陽進入白羊座0°，晝夜等長'),
          _buildInfoItem('夏至', '太陽進入巨蟹座0°，北半球白晝最長'),
          _buildInfoItem('秋分', '太陽進入天秤座0°，晝夜等長'),
          _buildInfoItem('冬至', '太陽進入摩羯座0°，北半球白晝最短'),
          const SizedBox(height: 12),
          Text(
            '這些時刻的星盤配置對個人和集體都有重要的象徵意義，可以用來分析季節能量的轉換和個人生活的節奏。',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Text(
            '$title：',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Expanded(
            child: Text(
              description,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取季節圖標
  IconData _getSeasonIcon(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Icons.local_florist; // 春分 - 花朵
      case SeasonType.summerSolstice:
        return Icons.wb_sunny; // 夏至 - 太陽
      case SeasonType.autumnEquinox:
        return Icons.eco; // 秋分 - 葉子
      case SeasonType.winterSolstice:
        return Icons.ac_unit; // 冬至 - 雪花
    }
  }

  /// 獲取季節顏色
  Color _getSeasonColor(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Colors.green; // 春分 - 綠色
      case SeasonType.summerSolstice:
        return AppColors.solarAmber; // 夏至 - 金黃色
      case SeasonType.autumnEquinox:
        return Colors.orange; // 秋分 - 橙色
      case SeasonType.winterSolstice:
        return Colors.blue; // 冬至 - 藍色
    }
  }

  /// 獲取下一個節氣
  SeasonData? _getNextSeason() {
    final now = DateTime.now();
    for (final season in _seasons) {
      if (season.dateTime.isAfter(now)) {
        return season;
      }
    }
    return null;
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// 年份選擇對話框
class _YearPickerDialog extends StatefulWidget {
  final int initialYear;
  final int minYear;
  final int maxYear;

  const _YearPickerDialog({
    required this.initialYear,
    required this.minYear,
    required this.maxYear,
  });

  @override
  State<_YearPickerDialog> createState() => _YearPickerDialogState();
}

class _YearPickerDialogState extends State<_YearPickerDialog> {
  late int _selectedYear;

  @override
  void initState() {
    super.initState();
    _selectedYear = widget.initialYear;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('選擇年份'),
      content: SizedBox(
        width: 300,
        height: 300,
        child: YearPicker(
          firstDate: DateTime(widget.minYear),
          lastDate: DateTime(widget.maxYear),
          selectedDate: DateTime(_selectedYear),
          onChanged: (date) {
            setState(() {
              _selectedYear = date.year;
            });
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(_selectedYear),
          child: const Text('確定'),
        ),
      ],
    );
  }
}

/// 節氣類型選擇對話框
class _SeasonTypePickerDialog extends StatefulWidget {
  final SeasonType? selectedType;

  const _SeasonTypePickerDialog({
    this.selectedType,
  });

  @override
  State<_SeasonTypePickerDialog> createState() => _SeasonTypePickerDialogState();
}

class _SeasonTypePickerDialogState extends State<_SeasonTypePickerDialog> {
  SeasonType? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('選擇節氣'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顯示全部選項
          RadioListTile<SeasonType?>(
            title: const Text('顯示全部'),
            subtitle: const Text('春分、夏至、秋分、冬至'),
            value: null,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value;
              });
            },
          ),
          const Divider(),

          // 各個節氣選項
          ...SeasonType.values.map((seasonType) {
            return RadioListTile<SeasonType?>(
              title: Text(seasonType.displayName),
              subtitle: Text(_getSeasonDescription(seasonType)),
              value: seasonType,
              groupValue: _selectedType,
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
              },
            );
          }),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(_selectedType),
          child: const Text('確定'),
        ),
      ],
    );
  }

  String _getSeasonDescription(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return '太陽進入白羊座 0°';
      case SeasonType.summerSolstice:
        return '太陽進入巨蟹座 0°';
      case SeasonType.autumnEquinox:
        return '太陽進入天秤座 0°';
      case SeasonType.winterSolstice:
        return '太陽進入摩羯座 0°';
    }
  }
}