import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

import '../../../viewmodels/files_viewmodel.dart';
import '../../../viewmodels/settings_viewmodel.dart';
import '../../../widgets/styled_card.dart';
import '../../AppTheme.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late SettingsViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    // 判断是否為開發模式
    const bool isInDevelopmentMode = kDebugMode;

    // 根據開發模式決定標籤頁數量
    const int tabCount = isInDevelopmentMode ? 4 : 3;

    _tabController = TabController(length: tabCount, vsync: this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Get the ViewModel from the provider
    _viewModel = Provider.of<SettingsViewModel>(context);
    // Initialize the TabController in the ViewModel
    _viewModel.initTabController(_tabController);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildAIModelSection() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        const Text(
          '選擇用於星盤解讀的 AI 模型：',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(
          _viewModel.models.length,
          (index) {
            final model = _viewModel.models[index];
            return StyledCard(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 16),
              borderRadius: BorderRadius.circular(12),
              isSelected: model.id == _viewModel.selectedModelId,
              onTap: () => _viewModel.saveSelectedModel(model.id),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        model.id == _viewModel.selectedModelId
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: AppColors.solarAmber,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        model.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.only(left: 36),
                    child: Text(
                      model.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.royalIndigo,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildChartDisplaySection() {
    if (_viewModel.chartSettings == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        // 宮位系統選擇
        const Text(
          '宮位系統：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _viewModel.chartSettings!.houseSystem,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: _viewModel.houseSystems.map((String system) {
            return DropdownMenuItem<String>(
              value: system,
              child: Text(system),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              _viewModel.updateHouseSystem(newValue);
            }
          },
        ),
        const SizedBox(height: 24),
        // 行星顯示設定
        const Text(
          '行星顯示：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ..._viewModel.chartSettings!.planetVisibility.entries.map((entry) {
          return CheckboxListTile(
            title: Text(entry.key),
            value: entry.value,
            onChanged: (bool? value) {
              if (value != null) {
                _viewModel.updatePlanetVisibility(entry.key, value);
              }
            },
          );
        }).toList(),
      ],
    );
  }

  Widget _buildAspectSection() {
    if (_viewModel.chartSettings == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        const Text(
          '相位容許度：',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ..._viewModel.chartSettings!.aspectOrbs.entries.map((entry) {
          final aspectName = entry.key;
          final currentValue = entry.value;

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$aspectName（${currentValue.toInt()}°）',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w500),
                ),
                Slider(
                  value: currentValue.clamp(0, 30),
                  min: 0,
                  max: 30,
                  divisions: 30,
                  label: '${currentValue.toInt()}°',
                  onChanged: (newValue) {
                    _viewModel.updateAspectOrb(aspectName, newValue);
                  },
                ),
                const Divider(height: 1),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildSystemSection() {
    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        // 獲取應用程序信息
        final String version = snapshot.hasData ? snapshot.data!.version : '未知';
        final String buildNumber =
            snapshot.hasData ? snapshot.data!.buildNumber : '未知';

        // 獲取當前日期時間
        final String buildDate =
            DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now());

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // 清除儲存資料與設定
            const Text(
              '資料管理',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '清除儲存資料與設定',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '清除所有儲存的資料與設定，包含出生資料、星盤設定、用戶資訊等。此操作不可還原。',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showClearDataConfirmDialog(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('清除所有資料'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 關於
            const Text(
              '關於',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: AppColors.royalIndigo.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.auto_awesome,
                            color: AppColors.royalIndigo,
                            size: 36,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Astreal 占星應用',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '版本: $version ($buildNumber)',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '日期: $buildDate',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '這是一款專業的占星應用，提供全面的占星功能，包括本命盤、合盤、推運盤等多種星盤類型，以及詳細的占星分析。',
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '© 2025 Astreal. 保留所有權利。',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 其他系統設定可以在這裡添加
            // - 主題設定
            // - 語言設定
            // - 資料備份/還原
            // - 通知設定
          ],
        );
      },
    );
  }

  // 顯示清除資料確認對話框
  Future<void> _showClearDataConfirmDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('確認清除所有資料'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: [
                Text('此操作將清除所有儲存的資料與設定，包含：'),
                SizedBox(height: 8),
                Text('- 所有出生資料'),
                Text('- 星盤設定與偏好'),
                Text('- 用戶資訊'),
                SizedBox(height: 8),
                Text('此操作不可還原，確定要繼續嗎？',
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.red)),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('確認清除', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                Navigator.of(context).pop();
                // 顯示載入中對話框
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return const AlertDialog(
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('正在清除資料...'),
                        ],
                      ),
                    );
                  },
                );

                try {
                  // 執行清除操作
                  await _viewModel.clearAllSettings();

                  // 清除 FilesViewModel 中的資料
                  final filesViewModel =
                      Provider.of<FilesViewModel>(context, listen: false);
                  filesViewModel.clearAllData(); // 直接清除內存中的資料

                  // 關閉載入中對話框
                  if (mounted) Navigator.of(context).pop();

                  // 顯示成功提示
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('所有資料已清除，設定已重置為預設值')),
                    );
                  }
                } catch (e) {
                  // 關閉載入中對話框
                  if (mounted) Navigator.of(context).pop();

                  // 顯示錯誤提示
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('清除資料時出錯: $e')),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  // 判斷是否為開發環境
  bool get _isInDevelopmentMode {
    // 在開發模式下，kReleaseMode 為 false
    return kDebugMode || kProfileMode;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsViewModel>(
      builder: (context, viewModel, _) {
        if (viewModel.isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('設定'),
            bottom: TabBar(
              controller: _tabController,
              labelColor: AppColors.solarAmber,
              unselectedLabelColor: Colors.white,
              indicatorColor: AppColors.solarAmber,
              indicatorWeight: 3,
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
              ),
              tabs: [
                if (_isInDevelopmentMode)
                  const Tab(
                    icon: Icon(Icons.psychology),
                    text: 'AI 模型',
                  ),
                const Tab(
                  icon: Icon(Icons.circle_outlined),
                  text: '星盤顯示',
                ),
                const Tab(
                  icon: Icon(Icons.auto_awesome),
                  text: '相位',
                ),
                const Tab(
                  icon: Icon(Icons.settings),
                  text: '系統',
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              if (_isInDevelopmentMode) _buildAIModelSection(),
              _buildChartDisplaySection(),
              _buildAspectSection(),
              _buildSystemSection(),
            ],
          ),
        );
      },
    );
  }
}
