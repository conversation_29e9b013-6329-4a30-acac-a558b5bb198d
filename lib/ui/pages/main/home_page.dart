import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/services/equinox_solstice_service.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:astreal/ui/widgets/person_selector_widget.dart';
import 'package:astreal/ui/widgets/transit_aspect_widgets.dart';
import 'package:astreal/utils/geocoding_service.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:astreal/viewmodels/home_viewmodel.dart';
import 'package:astreal/widgets/styled_card.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../utils/astrology_calculator.dart';
import '../booking_page.dart';
import '../chart_page.dart';
import '../chart_selection_page.dart';
import '../equinox_solstice_page.dart';
import '../location_picker_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // 添加 PageController 用於控制滑動
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    // 初始化 PageController（保留以避免其他地方可能使用）
    _pageController = PageController();
  }

  @override
  void dispose() {
    // 釋放 PageController
    _pageController.dispose();
    super.dispose();
  }

  // 導航到今日星相星盤頁面
  void _navigateToTodayChart(HomeViewModel viewModel) {
    try {
      // 創建 ChartData 對象
      final chartData = viewModel.createTodayChartData();

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) =>
                ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPage(chartData: chartData),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('無法載入今日星相: $e')),
      );
    }
  }

  // 選擇位置
  Future<void> _selectLocation(HomeViewModel viewModel) async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => const LocationPickerPage(),
      ),
    );

    if (result != null) {
      viewModel.updateLocation(
        result['name'],
        result['latitude'],
        result['longitude'],
      );
    }
  }



  // 編輯選中的人物資訊
  Future<void> _editSelectedPerson(HomeViewModel viewModel) async {
    if (viewModel.selectedPerson == null) return;

    final nameController =
        TextEditingController(text: viewModel.selectedPerson!.name);
    final placeController =
        TextEditingController(text: viewModel.selectedPerson!.birthPlace);
    final notesController =
        TextEditingController(text: viewModel.selectedPerson!.notes ?? '');
    DateTime selectedDate = viewModel.selectedPerson!.birthDate;
    TimeOfDay selectedTime = TimeOfDay(
      hour: viewModel.selectedPerson!.birthDate.hour,
      minute: viewModel.selectedPerson!.birthDate.minute,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('編輯出生資料'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: '姓名',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime(1900),
                          lastDate: DateTime.now(),
                        );
                        if (picked != null && picked != selectedDate) {
                          setDialogState(() {
                            selectedDate = picked;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: '出生日期',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          '${selectedDate.year}/${selectedDate.month}/${selectedDate.day}',
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    InkWell(
                      onTap: () async {
                        final TimeOfDay? picked = await showTimePicker(
                          context: context,
                          initialTime: selectedTime,
                        );
                        if (picked != null && picked != selectedTime) {
                          setDialogState(() {
                            selectedTime = picked;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: '出生時間',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          '${selectedTime.hour}:${selectedTime.minute.toString().padLeft(2, '0')}',
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    TextField(
                      controller: placeController,
                      decoration: const InputDecoration(
                        labelText: '出生地點',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        labelText: '備註',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('儲存'),
                  onPressed: () async {
                    if (nameController.text.isEmpty ||
                        placeController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('請填寫所有必填欄位')),
                      );
                      return;
                    }

                    final birthDateTime = DateTime(
                      selectedDate.year,
                      selectedDate.month,
                      selectedDate.day,
                      selectedTime.hour,
                      selectedTime.minute,
                    );

                    // 使用 GeocodingService 將出生地轉換為經緯度
                    Map<String, double>? coordinates =
                        await GeocodingService.getCoordinatesFromAddress(
                      placeController.text,
                    );

                    // 更新出生數據
                    final updatedData = BirthData(
                      id: viewModel.selectedPerson!.id,
                      name: nameController.text,
                      birthDate: birthDateTime,
                      birthPlace: placeController.text,
                      notes: notesController.text.isEmpty
                          ? null
                          : notesController.text,
                      latitude: coordinates['latitude']!,
                      longitude: coordinates['longitude']!,
                    );

                    // 更新數據
                    await viewModel.updateBirthData(updatedData);

                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('出生資料已更新')),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }



  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HomeViewModel(),
      child: Consumer<HomeViewModel>(builder: (context, viewModel, child) {
        return Scaffold(
            appBar: AppBar(
              title: const Text('首頁'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.location_on, color: Colors.white),
                  onPressed: () {
                    _selectLocation(viewModel);
                  },
                  tooltip: '選擇地點',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    viewModel.loadRecentAspects();
                  },
                  tooltip: '更新星象',
                ),
              ],
            ),
            body: viewModel.isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView(padding: const EdgeInsets.all(16), children: [
                    // // 只在開發環境下顯示預約功能
                    // if (_isInDevelopmentMode)
                    // 預約占星諮詢卡片
                    StyledCard(
                      elevation: 3,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const BookingPage(),
                          ),
                        );
                      },
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CardTitle(
                            title: '報名占星諮詢',
                            icon: Icons.event_available,
                            iconColor: Colors.green,
                            trailing: Icon(Icons.arrow_forward_ios, size: 16),
                          ),
                          SizedBox(height: 12),
                          Text(
                            '報名占星個人化諮詢服務，幫助你更深入地了解自己的星盤和生命旅程。',
                            style: TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),

                    // 個人資訊卡片 - 使用新的共用元件
                    PersonSelectorWidget(
                      selectedPerson: viewModel.selectedPerson,
                      personList: viewModel.birthDataList,
                      onPersonSelected: (person) {
                        viewModel.setSelectedPerson(person);
                      },
                      onEditPerson: (person) {
                        _editSelectedPerson(viewModel);
                      },
                      formatDateTime: viewModel.formatDateTime,
                      isLoading: viewModel.isLoadingBirthData,
                    ),

                    // 顯示行運盤資訊
                    if (viewModel.selectedPerson != null) ...[
                      const TransitAspectsSection(),

                      const SizedBox(height: 5),

                    ],

                    // 下一個節氣卡片
                    if (viewModel.nextSeason != null) ...[
                      _buildNextSeasonCard(viewModel),
                      const SizedBox(height: 16),
                    ],

                    // 今日星相卡片
                    StyledCard(
                      elevation: 3,
                      margin: const EdgeInsets.only(bottom: 16),
                      onTap: viewModel.recentAspects.isNotEmpty
                          ? () {
                              _navigateToTodayChart(viewModel);
                            }
                          : null,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 標題列
                          CardTitle(
                            title: '今日星相',
                            icon: Icons.stars,
                            iconColor: AppColors.solarAmber,
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TextButton.icon(
                                  onPressed: () {
                                    _selectLocation(viewModel);
                                  },
                                  icon: const Icon(Icons.location_on, size: 18),
                                  label: Text(viewModel.selectedLocation,
                                      style: const TextStyle(fontSize: 14)),
                                  style: TextButton.styleFrom(
                                    foregroundColor: AppColors.royalIndigo,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                  ),
                                ),
                                if (viewModel.recentAspects.isNotEmpty)
                                  IconButton(
                                    icon: const Icon(Icons.arrow_forward,
                                        size: 18),
                                    onPressed: () {
                                      _navigateToTodayChart(viewModel);
                                    },
                                    tooltip: '查看星盤',
                                    color: AppColors.royalIndigo,
                                  ),
                              ],
                            ),
                          ),

                          // 內容區域
                          if (viewModel.recentAspects.isEmpty)
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: Text(
                                  '今日沒有重要星相',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            )
                          else
                            // 顯示今日星相
                            Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: Builder(builder: (context) {
                                final dayAspects =
                                    viewModel.recentAspects[0]; // 只有一天的數據
                                final aspects = dayAspects['aspects'] as List;
                                final planets = dayAspects['planets'] as List;
                                final date = dayAspects['date'] as DateTime;

                                return SingleChildScrollView(
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Center(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 8),
                                            decoration: BoxDecoration(
                                              color: AppColors.royalIndigo
                                                  .withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Text(
                                              viewModel.formatDate(date),
                                              style: const TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color: AppColors.royalIndigo,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        // 顯示各行星的星座位置
                                        const Row(
                                          children: [
                                            Icon(Icons.public,
                                                size: 18,
                                                color: AppColors.royalIndigo),
                                            SizedBox(width: 8),
                                            Text(
                                              '行星位置',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: AppColors.royalIndigo,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Wrap(
                                          spacing: 8,
                                          runSpacing: 8,
                                          children: planets.map((planet) {
                                            return Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 10,
                                                vertical: 6,
                                              ),
                                              decoration: BoxDecoration(
                                                color: AppColors.royalIndigo
                                                    .withOpacity(0.08),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: AppColors.royalIndigo
                                                      .withOpacity(0.2),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Text(
                                                '${planet.name} ${planet.sign}',
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  color: AppColors.royalIndigo,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                        ),
                                        // 相位部分
                                        if (aspects.isNotEmpty) ...[
                                          const SizedBox(height: 8),
                                          const Row(
                                            children: [
                                              Icon(Icons.compare_arrows,
                                                  size: 18,
                                                  color: AppColors.royalIndigo),
                                              SizedBox(width: 8),
                                              Text(
                                                '今日相位',
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                  color: AppColors.royalIndigo,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          // 按重要性排序相位
                                          ...(() {
                                            final aspectsWithImportance =
                                                aspects
                                                    .map((aspect) => {
                                                          'aspect': aspect,
                                                          'importance': viewModel
                                                              .getAspectImportance(
                                                                  aspect.aspect,
                                                                  aspect.planet1
                                                                      .name,
                                                                  aspect.planet2
                                                                      .name)
                                                        })
                                                    .toList();

                                            aspectsWithImportance.sort((a, b) =>
                                                (b['importance'] as int)
                                                    .compareTo(a['importance']
                                                        as int));

                                            return aspectsWithImportance;
                                          })()
                                              .map((item) {
                                            final aspect =
                                                item['aspect'] as AspectInfo;
                                            final importance =
                                                item['importance'] as int;
                                            final aspectColor =
                                                AstrologyCalculator
                                                    .getAspectColor(
                                                        aspect.aspect);
                                            final meaning = viewModel
                                                .getAspectForecast(aspect);

                                            return Container(
                                              margin: const EdgeInsets.only(
                                                  bottom: 10),
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: aspectColor
                                                    .withOpacity(0.05),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: aspectColor
                                                      .withOpacity(0.2),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 8,
                                                          vertical: 2,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: aspectColor
                                                              .withOpacity(0.1),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        child: Text(
                                                          aspect.symbol,
                                                          style: TextStyle(
                                                            fontFamily:
                                                                "astro_one_font",
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: aspectColor,
                                                          ),
                                                        ),
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Expanded(
                                                        child: Text(
                                                          '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name}${aspect.direction != null ? ' (${aspect.getDirectionText()} ${aspect.orb.toStringAsFixed(2)}°)' : ''}',
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 12),
                                                        ),
                                                      ),
                                                      if (importance >= 3)
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal: 6,
                                                                  vertical: 2),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: AppColors
                                                                .warning
                                                                .withOpacity(
                                                                    0.2),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: const Text(
                                                            '重要',
                                                            style: TextStyle(
                                                              fontSize: 10,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: AppColors
                                                                  .warning,
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            );
                                          }),
                                        ],
                                      ]),
                                );
                              }),
                            ),
                        ],
                      ),
                    )
                  ]));
      }),
    );
  }

  /// 構建下一個節氣卡片
  Widget _buildNextSeasonCard(HomeViewModel viewModel) {
    final nextSeason = viewModel.nextSeason!;
    final now = DateTime.now();
    final daysUntil = nextSeason.dateTime.difference(now).inDays;

    // 獲取季節圖標和顏色
    final seasonIcon = _getSeasonIcon(nextSeason.seasonType);
    final seasonColor = _getSeasonColor(nextSeason.seasonType);

    return StyledCard(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      onTap: () {
        _navigateToEquinoxSolstice(viewModel);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              seasonColor.withOpacity(0.1),
              seasonColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題列
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: seasonColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      seasonIcon,
                      color: seasonColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '下一個節氣：${nextSeason.seasonType.displayName}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _formatSeasonDateTime(nextSeason.dateTime),
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Text(
                        '$daysUntil',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: seasonColor,
                        ),
                      ),
                      Text(
                        '天後',
                        style: TextStyle(
                          fontSize: 12,
                          color: seasonColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 節氣描述
              Text(
                _getSeasonDescription(nextSeason.seasonType),
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  height: 1.3,
                ),
              ),

              // 如果有本命盤人物，顯示個人影響提示
              if (viewModel.selectedPerson != null) ...[
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _navigateToSeasonComparison(viewModel, nextSeason),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: AppColors.royalIndigo.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 16,
                          color: AppColors.royalIndigo,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            '點擊查看與 ${viewModel.selectedPerson!.name} 的比較盤',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.royalIndigo,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppColors.royalIndigo.withOpacity(0.7),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取季節圖標
  IconData _getSeasonIcon(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Icons.local_florist; // 春分 - 花朵
      case SeasonType.summerSolstice:
        return Icons.wb_sunny; // 夏至 - 太陽
      case SeasonType.autumnEquinox:
        return Icons.eco; // 秋分 - 葉子
      case SeasonType.winterSolstice:
        return Icons.ac_unit; // 冬至 - 雪花
    }
  }

  /// 獲取季節顏色
  Color _getSeasonColor(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Colors.green; // 春分 - 綠色
      case SeasonType.summerSolstice:
        return AppColors.solarAmber; // 夏至 - 金黃色
      case SeasonType.autumnEquinox:
        return Colors.orange; // 秋分 - 橙色
      case SeasonType.winterSolstice:
        return Colors.blue; // 冬至 - 藍色
    }
  }

  /// 獲取季節描述
  String _getSeasonDescription(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return '太陽進入白羊座，晝夜等長，象徵新的開始與成長。';
      case SeasonType.summerSolstice:
        return '太陽進入巨蟹座，北半球白晝最長，陽氣最盛。';
      case SeasonType.autumnEquinox:
        return '太陽進入天秤座，晝夜等長，象徵平衡與收穫。';
      case SeasonType.winterSolstice:
        return '太陽進入摩羯座，北半球白晝最短，陰氣最盛。';
    }
  }

  /// 格式化季節日期時間
  String _formatSeasonDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 導航到二分二至圖頁面
  void _navigateToEquinoxSolstice(HomeViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EquinoxSolsticePage(
          natalPerson: viewModel.selectedPerson,
          location: BirthData(
            id: 'current_location',
            name: viewModel.selectedLocation,
            birthDate: DateTime.now(),
            latitude: viewModel.latitude,
            longitude: viewModel.longitude,
            birthPlace: viewModel.selectedLocation,
          ),
        ),
      ),
    );
  }

  /// 導航到節氣比較盤
  void _navigateToSeasonComparison(HomeViewModel viewModel, SeasonData nextSeason) {
    if (viewModel.selectedPerson == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChartSelectionPage(
          primaryPerson: viewModel.selectedPerson!,
          secondaryPerson: BirthData(
            id: 'season_${nextSeason.seasonType.name}',
            name: '${nextSeason.seasonType.displayName} (${DateTime.now().year}年)',
            birthDate: nextSeason.dateTime,
            latitude: viewModel.latitude,
            longitude: viewModel.longitude,
            birthPlace: viewModel.selectedLocation,
          ),
          initialChartType: ChartType.synastry, // 設定為合盤
          isChangingChartType: false,
        ),
      ),
    );
  }
}
