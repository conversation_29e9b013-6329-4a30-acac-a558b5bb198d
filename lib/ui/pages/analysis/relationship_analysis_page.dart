import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../models/birth_data.dart';
import '../../../ui/AppTheme.dart';
import '../../../ui/widgets/relationship_symbols_view.dart';
import '../../../viewmodels/relationship_analysis_viewmodel.dart';

class RelationshipAnalysisPage extends StatefulWidget {
  final String title;
  final String description;

  const RelationshipAnalysisPage({
    super.key,
    required this.title,
    required this.description,
  });

  @override
  State<RelationshipAnalysisPage> createState() => _RelationshipAnalysisPageState();
}

class _RelationshipAnalysisPageState extends State<RelationshipAnalysisPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late RelationshipAnalysisViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _viewModel = RelationshipAnalysisViewModel();
  }

  /// 顯示用戶選擇對話框
  Future<void> _showPersonSelectionDialog() async {
    final List<BirthData> birthDataList = await _viewModel.loadBirthData();

    if (birthDataList.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
        );
      }
      return;
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('選擇出生數據'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: birthDataList.length,
              itemBuilder: (context, index) {
                final data = birthDataList[index];
                return ListTile(
                  title: Text(data.name),
                  subtitle: Text(
                    '${data.birthDate.year}-${data.birthDate.month.toString().padLeft(2, '0')}-${data.birthDate.day.toString().padLeft(2, '0')} ${data.birthPlace}',
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _analyzeRelationshipSymbols(data);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      );
    }
  }

  /// 分析戀愛象徵
  Future<void> _analyzeRelationshipSymbols(BirthData birthData) async {
    // 顯示加載指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );

    try {
      // 使用 ViewModel 分析戀愛象徵
      await _viewModel.analyzeRelationshipSymbols(birthData);

      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示分析結果
      if (mounted && _viewModel.chartData != null) {
        _showRelationshipAnalysisResults(_viewModel.chartData!);
      }
    } catch (e) {
      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分析時出錯: $e')),
        );
      }
    }
  }

  /// 顯示戀愛分析結果
  void _showRelationshipAnalysisResults(final chartData) {
    // 顯示分析結果對話框
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return ChangeNotifierProvider.value(
          value: _viewModel,
          child: RelationshipSymbolsView(chartData: chartData),
        );
      },
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.solarAmber,
          unselectedLabelColor: Colors.white,
          indicatorColor: AppColors.solarAmber,
          indicatorWeight: 3,
          isScrollable: true,
          tabs: const [
            Tab(text: '本命盤分析'),
            Tab(text: '次限推運'),
            Tab(text: '行運分析'),
            Tab(text: '日蝕/月蝕'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNatalChartAnalysis(),
          _buildSecondaryProgressionsAnalysis(),
          _buildTransitsAnalysis(),
          _buildEclipsesAnalysis(),
        ],
      ),
    );
  }

  Widget _buildNatalChartAnalysis() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('本命盤戀愛分析', Icons.favorite),
          const SizedBox(height: 16),
          _buildAnalysisCard(
            '金星、火星、7宮主、5宮主等戀愛象徵',
            '這些行星與宮位的落點與相位分析',
            Icons.favorite_border,
            content: '金星代表您的愛情觀和吸引力，火星代表您的慾望和追求方式，第7宮主宰您的伴侶類型，第5宮則關乎戀愛體驗。\n\n'
                '通過分析這些行星的星座落點、宮位位置以及它們之間的相位關係，可以揭示您在戀愛中的行為模式、吸引力類型以及潛在的挑戰。',
          ),
          _buildAnalysisCard(
            '喜好、吸引力與戀愛偏好類型',
            '基於行星位置的個人偏好分析',
            Icons.person_outline,
            content: '您的本命盤揭示了您在戀愛中的自然傾向和偏好。金星的星座位置顯示您欣賞的美感和價值觀，而火星則揭示您如何追求所愛。\n\n'
                '第7宮的守護星和落入第7宮的行星，能夠描繪出您理想伴侶的特質和您在關係中的期望。這些元素共同構成了您獨特的戀愛模式和吸引力類型。',
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryProgressionsAnalysis() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('次限推運分析', Icons.update),
          const SizedBox(height: 16),
          _buildAnalysisCard(
            '金星進入新宮位或與重要行星形成相位',
            '內在戀愛需求的轉變',
            Icons.change_circle_outlined,
            content: '次限推運反映了您內在心理和情感的漸進式發展。當推運金星進入新的宮位或與其他行星形成重要相位時，這通常標誌著您的戀愛需求和價值觀正在轉變。\n\n'
                '這些轉變可能會改變您被吸引的對象類型，或者改變您在關係中的行為模式和期望。理解這些內在變化，有助於您更好地適應新的戀愛階段。',
          ),
        ],
      ),
    );
  }

  Widget _buildTransitsAnalysis() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('行運分析', Icons.timeline),
          const SizedBox(height: 16),
          _buildAnalysisCard(
            '木星/天王星進入第5或第7宮',
            '與金星/7宮主成良相的時機',
            Icons.star_outline,
            content: '當木星或天王星行運進入您的第5宮（戀愛）或第7宮（伴侶關係），或與您的本命金星或第7宮主形成良好相位時，這通常是戀愛機會增加的時期。\n\n'
                '木星帶來擴展和好運，可能會引入新的戀愛機會或加深現有關係。天王星則帶來突破和意外，可能會讓您遇到非常規或令人驚喜的對象。',
          ),
          _buildAnalysisCard(
            '土星的考驗與穩定',
            '關係中的成長與承諾',
            Icons.balance,
            content: '土星行運雖然常被視為挑戰，但實際上它提供了穩定和成長的機會。當土星行運接觸您的戀愛相關行星或宮位時，這可能是關係面臨考驗的時期。\n\n'
                '這些考驗通常圍繞著承諾、責任和長期規劃。成功度過土星的考驗，往往會使關係更加穩固和成熟。這是檢視關係是否有長期發展潛力的重要時期。',
          ),
        ],
      ),
    );
  }

  Widget _buildEclipsesAnalysis() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('日蝕/月蝕分析', Icons.brightness_4),
          const SizedBox(height: 16),
          _buildAnalysisCard(
            '觸及關係宮位或行星的蝕相',
            '關係的轉折點',
            Icons.change_history,
            content: '日蝕和月蝕是強大的天文現象，當它們發生在與您的關係宮位或行星相關的位置時，通常標誌著關係中的重要轉折點。\n\n'
                '日蝕代表新的開始，可能帶來新的關係或現有關係的新階段。月蝕則與結束和釋放有關，可能標誌著關係中某些方面的完成或轉變。\n\n'
                '蝕相的影響通常在發生前後幾個月都能感受到，理解這些能量有助於您更好地導航關係中的重大變化。',
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppColors.royalIndigo, size: 28),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisCard(String title, String subtitle, IconData icon, {required String content, Color? color}) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (title == '金星、火星、7宮主、5宮主等戀愛象徵') {
            _showPersonSelectionDialog();
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    backgroundColor: color ?? AppColors.solarAmber,
                    radius: 24,
                    child: Icon(icon, color: Colors.white, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: const TextStyle(fontSize: 14, color: Colors.black87),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                content,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
