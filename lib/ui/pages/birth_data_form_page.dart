import 'package:flutter/material.dart' hide DatePickerTheme;
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:uuid/uuid.dart';

import '../../models/birth_data.dart';
import '../../utils/LoggerUtils.dart';
import '../../utils/geocoding_service.dart';
import '../AppTheme.dart';

/// 出生資料表單頁面，用於創建或編輯出生資料
class BirthDataFormPage extends StatefulWidget {
  final BirthData? initialData;

  const BirthDataFormPage({Key? key, this.initialData}) : super(key: key);

  @override
  State<BirthDataFormPage> createState() => _BirthDataFormPageState();
}

class _BirthDataFormPageState extends State<BirthDataFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _placeController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();
  final _timeController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // 初始化日期和時間控制器
    _updateTextControllers();

    // 如果有初始數據，則填充表單
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!.name;
      _placeController.text = widget.initialData!.birthPlace;
      _notesController.text = widget.initialData!.notes ?? '';
      _selectedDate = widget.initialData!.birthDate;
      _selectedTime = TimeOfDay(
        hour: widget.initialData!.birthDate.hour,
        minute: widget.initialData!.birthDate.minute,
      );

      // 更新日期和時間控制器
      _updateTextControllers();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _placeController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  // 更新日期和時間文本控制器
  void _updateTextControllers() {
    _dateController.text = '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}';
    _timeController.text = '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';
  }

  // 根據文本更新日期
  void _updateDateFromText(String text) {
    try {
      // 嘗試解析日期字符串
      final parts = text.split('-');
      if (parts.length == 3) {
        final year = int.tryParse(parts[0]);
        final month = int.tryParse(parts[1]);
        final day = int.tryParse(parts[2]);

        if (year != null && month != null && day != null) {
          // 驗證日期是否有效
          if (year >= 1900 && year <= DateTime.now().year &&
              month >= 1 && month <= 12 &&
              day >= 1 && day <= 31) {
            // 創建新的 DateTime 對象
            final newDate = DateTime(year, month, day,
                _selectedDate.hour, _selectedDate.minute);

            // 驗證日期是否在合理範圍內
            if (newDate.isBefore(DateTime.now()) &&
                newDate.isAfter(DateTime(1900))) {
              setState(() {
                _selectedDate = newDate;
              });
            }
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析日期失敗: $e');
    }
  }

  // 根據文本更新時間
  void _updateTimeFromText(String text) {
    try {
      // 嘗試解析時間字符串
      final parts = text.split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]);
        final minute = int.tryParse(parts[1]);

        if (hour != null && minute != null) {
          // 驗證時間是否有效
          if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
            setState(() {
              _selectedTime = TimeOfDay(hour: hour, minute: minute);
            });
          }
        }
      }
    } catch (e) {
      // 如果解析失敗，不做任何變更
      logger.e('解析時間失敗: $e');
    }
  }

  // 選擇日期
  Future<void> _selectDate() async {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1800, 1, 1),
      maxTime: DateTime.now(),
      onConfirm: (date) {
        setState(() {
          _selectedDate = date;
          // 更新日期文本控制器
          _dateController.text = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        });
      },
      currentTime: _selectedDate,
      locale: LocaleType.tw,
      theme: const DatePickerTheme(
        backgroundColor: Colors.white,
        itemStyle: TextStyle(color: Colors.black, fontSize: 18),
        doneStyle: TextStyle(color: AppColors.royalIndigo, fontSize: 16),
        cancelStyle: TextStyle(color: Colors.grey, fontSize: 16),
        containerHeight: 210.0,
        titleHeight: 44.0,
        itemHeight: 40.0,
      ),
    );
  }

  // 選擇時間
  Future<void> _selectTime() async {
    DatePicker.showTimePicker(
      context,
      showTitleActions: true,
      onConfirm: (time) {
        setState(() {
          _selectedTime = TimeOfDay(hour: time.hour, minute: time.minute);
          // 更新時間文本控制器
          _timeController.text = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        });
      },
      currentTime: DateTime(
        2000, 1, 1,
        _selectedTime.hour,
        _selectedTime.minute,
      ),
      locale: LocaleType.tw,
      theme: const DatePickerTheme(
        backgroundColor: Colors.white,
        itemStyle: TextStyle(color: Colors.black, fontSize: 18),
        doneStyle: TextStyle(color: AppColors.royalIndigo, fontSize: 16),
        cancelStyle: TextStyle(color: Colors.grey, fontSize: 16),
        containerHeight: 210.0,
        titleHeight: 44.0,
        itemHeight: 40.0,
      ),
    );
  }

  // 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取當前位置，請確保已開啟位置服務和權限')),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 根據經緯度獲取地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _placeController.text = address;
          _isLoading = false;
        });

        // 顯示成功訊息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr = '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _placeController.text = locationStr;
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('獲取當前位置時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('獲取當前位置時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 保存出生資料
  Future<void> _saveBirthData() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 先確保日期和時間已經更新
    _updateDateFromText(_dateController.text);
    _updateTimeFromText(_timeController.text);

    setState(() {
      _isLoading = true;
    });

    try {
      // 使用 GeocodingService 將出生地轉換為經緯度
      Map<String, double>? coordinates = await GeocodingService.getCoordinatesFromAddress(
        _placeController.text,
      );

      if (coordinates == null || coordinates['latitude'] == null || coordinates['longitude'] == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取出生地的經緯度，請輸入有效的地點')),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 創建出生日期時間
      final birthDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // 創建或更新出生資料
      final birthData = BirthData(
        id: widget.initialData?.id ?? const Uuid().v4(),
        name: _nameController.text,
        birthDate: birthDateTime,
        birthPlace: _placeController.text,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        latitude: coordinates['latitude']!,
        longitude: coordinates['longitude']!,
      );

      // 返回創建或更新的出生資料
      if (mounted) {
        Navigator.of(context).pop(birthData);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存出生資料時出錯: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.initialData == null ? '新增出生資料' : '編輯出生資料'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 姓名
                    const Text(
                      '姓名',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        hintText: '請輸入姓名',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '請輸入姓名';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 出生日期和時間
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '出生日期和時間',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            TextButton.icon(
                              onPressed: _selectDate,
                              icon: const Icon(Icons.calendar_today, size: 16),
                              label: const Text('選擇日期'),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.royalIndigo,
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: _selectTime,
                              icon: const Icon(Icons.access_time, size: 16),
                              label: const Text('選擇時間'),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.royalIndigo,
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        // 日期輸入
                        Expanded(
                          child: TextFormField(
                            controller: _dateController,
                            decoration: InputDecoration(
                              hintText: 'YYYY-MM-DD',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.calendar_today),
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.more_horiz),
                                tooltip: '選擇日期',
                                onPressed: _selectDate,
                              ),
                            ),
                            keyboardType: TextInputType.datetime,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '請輸入出生日期';
                              }
                              // 簡單的日期格式驗證
                              final datePattern = RegExp(r'^\d{4}-\d{2}-\d{2}$');
                              if (!datePattern.hasMatch(value)) {
                                return '請使用 YYYY-MM-DD 格式';
                              }
                              return null;
                            },
                            onChanged: _updateDateFromText,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // 時間輸入
                        Expanded(
                          child: TextFormField(
                            controller: _timeController,
                            decoration: InputDecoration(
                              hintText: 'HH:MM',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.access_time),
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.more_horiz),
                                tooltip: '選擇時間',
                                onPressed: _selectTime,
                              ),
                            ),
                            keyboardType: TextInputType.datetime,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '請輸入出生時間';
                              }
                              // 簡單的時間格式驗證
                              final timePattern = RegExp(r'^([01]?[0-9]|2[0-3]):([0-5][0-9])$');
                              if (!timePattern.hasMatch(value)) {
                                return '請使用 HH:MM 格式';
                              }
                              return null;
                            },
                            onChanged: _updateTimeFromText,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 出生地
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '出生地',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton.icon(
                          onPressed: _getCurrentLocation,
                          icon: const Icon(Icons.my_location, size: 16),
                          label: const Text('取得當前位置'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.royalIndigo,
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _placeController,
                      decoration: const InputDecoration(
                        hintText: '請輸入出生地（如：台北市）',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on_outlined),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '請輸入出生地';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 備註
                    const Text(
                      '備註（選填）',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText: '請輸入備註',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),

                    // 保存按鈕
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveBirthData,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.royalIndigo,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          widget.initialData == null ? '新增' : '保存',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
