import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/chart_data.dart';
import '../../models/chart_type.dart';
import '../../utils/chart_pdf_generator.dart';
import '../../viewmodels/chart_page_viewmodel.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../../viewmodels/recent_charts_viewmodel.dart';
import '../../widgets/aspect_table_widget.dart';
import '../../widgets/chart_elements_widget.dart';
import '../../widgets/chart_view_widget.dart';
import '../../widgets/classical_astrology_widget.dart';
import '../../widgets/firdaria_widget.dart';
import '../../widgets/houses_widget.dart';
import '../../widgets/planet_list_widget.dart';
import '../AppTheme.dart';
import '../dialogs/copy_options_dialog.dart';
import 'chart_interpretation_page.dart';
import 'chart_selection_page.dart';
import 'main/settings_page.dart';

class ChartPage extends StatefulWidget {
  final ChartData? chartData;
  final ChartType? chartType;

  const ChartPage({super.key, this.chartData, this.chartType});

  @override
  _ChartPageState createState() => _ChartPageState();
}

class _ChartPageState extends State<ChartPage>
    with TickerProviderStateMixin {
  ChartPageViewModel? _pageViewModel;
  ChartType? _lastChartType;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // The ChartViewModel is provided by the parent widget
    final chartViewModel =
    Provider.of<ChartViewModel>(context, listen: false);
    // 如果提供了 chartType，設置星盤類型
    if (widget.chartType != null) {
      chartViewModel.setChartType(widget.chartType!, context: context);
    }
    if (_pageViewModel == null) {
      final recentChartsViewModel =
          Provider.of<RecentChartsViewModel>(context, listen: false);
      // 記錄到最近使用的星盤
      recentChartsViewModel.addOrUpdateRecentChart(chartViewModel.chartData);

      // 記錄初始星盤類型
      _lastChartType = chartViewModel.chartType;
      _pageViewModel = ChartPageViewModel(chartViewModel, this);
    }
  }

  /// 檢查並更新 TabController（如果需要）
  void _checkAndUpdateTabController(ChartViewModel viewModel) {
    if (_lastChartType != viewModel.chartType) {
      final currentTabCount = _pageViewModel!.tabController.length;
      final newTabCount = viewModel.chartType == ChartType.firdaria ? 7 : 6;

      if (currentTabCount != newTabCount) {
        final currentIndex = _pageViewModel!.tabController.index;

        // 處置舊的 TabController
        _pageViewModel!.tabController.dispose();

        // 創建新的 ChartPageViewModel
        _pageViewModel = ChartPageViewModel(viewModel, this);

        // 嘗試保持當前選中的標籤頁
        if (currentIndex < newTabCount) {
          _pageViewModel!.tabController.index = currentIndex;
        }
      }

      _lastChartType = viewModel.chartType;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_pageViewModel == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Consumer<ChartViewModel>(
      builder: (context, viewModel, _) {
        // 檢查並更新 TabController
        _checkAndUpdateTabController(viewModel);

        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(viewModel.getChartTitle()),
            actions: [
              // 整合所有功能的選單按鈕
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                tooltip: '功能選單',
                onSelected: (String action) async {
                  switch (action) {
                    case 'chart_type':
                      // 導航到星盤選擇頁面
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ChartSelectionPage(
                            primaryPerson: viewModel.primaryPerson,
                            secondaryPerson: viewModel.secondaryPerson,
                            initialChartType: viewModel.chartType,
                            isChangingChartType: true,
                          ),
                        ),
                      );

                      // 如果返回了新的 ChartData，更新星盤
                      if (result is ChartData && mounted) {
                        // 使用返回的 ChartData 更新星盤
                        viewModel.updateChartData(result);
                      }
                      break;

                    case 'settings':
                      // 使用 async/await 等待設定頁面關閉
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SettingsPage(),
                        ),
                      );
                      // 設定頁面關閉後，重新計算星盤數據
                      if (!context.mounted) return;
                      await viewModel.calculateChart();
                      break;

                    case 'ai_interpretation':
                      // 只在開發模式下執行
                      if (kDebugMode) {
                        if (viewModel.interpretationViewModel.isLoading) return;

                        if (viewModel
                            .interpretationViewModel.interpretation.isEmpty) {
                          await viewModel.getAIInterpretation();
                        }
                        if (mounted &&
                            viewModel.interpretationViewModel.interpretation
                                .isNotEmpty) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ChartInterpretationPage(
                                interpretation: viewModel
                                    .interpretationViewModel.interpretation,
                              ),
                            ),
                          );
                        }
                      }
                      break;

                    case 'copy_info':
                      // 只在開發模式下執行
                      if (kDebugMode) {
                        if (!viewModel.isCopying) {
                          // 顯示複製選項對話框
                          final options = await showCopyOptionsDialog(context);

                          // 如果用戶選擇了選項（沒有取消）
                          if (options != null && mounted) {
                            // 將用戶選擇的選項傳遞給 copyChartInfo 方法
                            final success =
                                await viewModel.copyChartInfo(options: options);
                            if (mounted && success) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('星盤資訊已複製到剪貼板'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            }
                          }
                        }
                      }
                      break;

                    case 'generate_pdf':
                      // 只在開發模式下執行
                      if (kDebugMode) {
                        if (!viewModel.isGeneratingPdf) {
                          try {
                            final action = await _pageViewModel!
                                .showPdfOptionsDialog(context);
                            if (action != null && mounted) {
                              try {
                                // 顯示生成中的提示
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('正在生成 PDF，請稍候...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );

                                final pdfBytes = await viewModel.generatePdf();
                                if (pdfBytes != null && mounted) {
                                  // 清除之前的 SnackBar
                                  ScaffoldMessenger.of(context)
                                      .hideCurrentSnackBar();

                                  switch (action) {
                                    case 'preview':
                                      await ChartPdfGenerator.previewPdf(
                                        context: context,
                                        pdfBytes: pdfBytes,
                                        title:
                                            '${viewModel.primaryPerson.name}的星盤數據',
                                      );
                                      break;
                                    case 'share':
                                      await ChartPdfGenerator.savePdfAndShare(
                                        context: context,
                                        pdfBytes: pdfBytes,
                                        fileName:
                                            '${viewModel.primaryPerson.name}_星盤數據.pdf',
                                      );
                                      break;
                                  }
                                } else if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('無法生成 PDF，請確保星盤數據已計算完成'),
                                      duration: Duration(seconds: 5),
                                    ),
                                  );
                                }
                              } catch (e) {
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          '生成 PDF 失敗: ${e.toString().replaceAll('Exception: ', '')}'),
                                      duration: const Duration(seconds: 5),
                                      action: SnackBarAction(
                                        label: '知道了',
                                        onPressed: () {},
                                      ),
                                    ),
                                  );
                                }
                              }
                            }
                          } catch (dialogError) {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('顯示 PDF 選項對話框失敗: $dialogError'),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        } else if (mounted) {
                          // 如果已經在生成 PDF，顯示提示
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('正在生成 PDF，請稍候...'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        }
                      }
                      break;

                    case 'send_email':
                      // 只在開發模式下執行
                      if (kDebugMode) {
                        if (!viewModel.isSendingEmail) {
                          final result = await _pageViewModel!
                              .showEmailInputDialog(context);
                          if (result != null) {
                            final success = await viewModel.sendEmail(
                              email: result['email'],
                              useHtml: result['useHtml'],
                            );
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(success
                                      ? '星盤數據已發送至 ${result['email']}'
                                      : '發送失敗，請稍後再試'),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        }
                      }
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => [
                  // 設定（所有模式下都顯示）
                  const PopupMenuItem<String>(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('設定'),
                      ],
                    ),
                  ),

                  // 以下功能只在開發模式下顯示
                  if (kDebugMode) ...[
                    // 切換星盤類型
                    const PopupMenuItem<String>(
                      value: 'chart_type',
                      child: Row(
                        children: [
                          Icon(Icons.swap_horiz, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('切換星盤類型'),
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'ai_interpretation',
                      enabled: !viewModel.interpretationViewModel.isLoading,
                      child: Row(
                        children: [
                          const Icon(Icons.psychology,
                              color: AppColors.royalIndigo),
                          const SizedBox(width: 12),
                          const Text('AI 解讀'),
                          if (viewModel.interpretationViewModel.isLoading) ...[
                            const SizedBox(width: 8),
                            const SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ],
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'copy_info',
                      enabled: !viewModel.isCopying,
                      child: Row(
                        children: [
                          const Icon(Icons.content_copy,
                              color: AppColors.royalIndigo),
                          const SizedBox(width: 12),
                          const Text('複製星盤資訊'),
                          if (viewModel.isCopying) ...[
                            const SizedBox(width: 8),
                            const SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ],
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'generate_pdf',
                      enabled: !viewModel.isGeneratingPdf,
                      child: Row(
                        children: [
                          const Icon(Icons.picture_as_pdf,
                              color: AppColors.royalIndigo),
                          const SizedBox(width: 12),
                          const Text('生成 PDF'),
                          if (viewModel.isGeneratingPdf) ...[
                            const SizedBox(width: 8),
                            const SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ],
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'send_email',
                      enabled: !viewModel.isSendingEmail,
                      child: Row(
                        children: [
                          const Icon(Icons.email, color: AppColors.royalIndigo),
                          const SizedBox(width: 12),
                          const Text('發送郵件'),
                          if (viewModel.isSendingEmail) ...[
                            const SizedBox(width: 8),
                            const SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ], // 結束開發模式功能
                ],
              ),
            ],
            bottom: TabBar(
              controller: _pageViewModel!.tabController,
              labelColor: AppColors.solarAmber,
              unselectedLabelColor: Colors.white,
              indicatorColor: AppColors.solarAmber,
              indicatorWeight: 3,
              isScrollable: true,
              // 設置為可滑動
              tabAlignment: TabAlignment.center,
              // 對齊方式
              labelPadding: const EdgeInsets.symmetric(horizontal: 16.0),
              // 增加標籤間距
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
              ),
              tabs: [
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.circle_outlined),
                      SizedBox(width: 8),
                      Text('星盤圖'),
                    ],
                  ),
                ),
                // 法達盤標籤頁，只在法達盤時顯示
                if (viewModel.chartType == ChartType.firdaria)
                  const Tab(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.hourglass_full),
                        SizedBox(width: 8),
                        Text('法達盤'),
                      ],
                    ),
                  ),
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.list),
                      SizedBox(width: 8),
                      Text('行星位置'),
                    ],
                  ),
                ),
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.home_outlined),
                      SizedBox(width: 8),
                      Text('宮位'),
                    ],
                  ),
                ),
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.grid_on),
                      SizedBox(width: 8),
                      Text('相位表'),
                    ],
                  ),
                ),
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.pie_chart),
                      SizedBox(width: 8),
                      Text('統計'),
                    ],
                  ),
                ),
                const Tab(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.auto_awesome),
                      SizedBox(width: 8),
                      Text('古典占星'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          body: viewModel.isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _pageViewModel!.tabController,
                  children: [
                    ChartViewWidget(viewModel: viewModel),
                    // 法達盤內容，無論是否為法達盤都顯示，但只有法達盤時才會顯示法達盤標籤
                    if (viewModel.chartType == ChartType.firdaria)
                      FirdariaWidget(viewModel: viewModel),
                    PlanetListWidget(viewModel: viewModel),
                    HousesWidget(viewModel: viewModel),
                    AspectTableWidget(viewModel: viewModel),
                    ChartElementsWidget(viewModel: viewModel),
                    ClassicalAstrologyWidget(viewModel: viewModel),
                  ],
                ),
        );
      },
    );
  }
}
