import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'AppTheme.dart';

/// Manages the app's theme state
class ThemeProvider extends ChangeNotifier {
  static const String _themePreferenceKey = 'theme_preference';
  static const String _themeModeKey = 'theme_mode';

  ThemeMode _themeMode = ThemeMode.light;
  
  ThemeProvider() {
    _loadThemePreference();
  }

  /// Current theme mode
  ThemeMode get themeMode => _themeMode;

  /// Current theme data
  ThemeData get themeData {
    switch (_themeMode) {
      case ThemeMode.dark:
        // Currently using light theme for both modes
        return AstrealAppTheme.lightTheme;
      case ThemeMode.light:
      default:
        return AstrealAppTheme.lightTheme;
    }
  }

  /// Toggle between light and dark theme
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    _saveThemePreference();
    notifyListeners();
  }

  /// Set specific theme mode
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveThemePreference();
    notifyListeners();
  }

  /// Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt(_themeModeKey) ?? ThemeMode.light.index;
      _themeMode = ThemeMode.values[themeModeIndex];
      notifyListeners();
    } catch (e) {
      // Default to light theme if there's an error
      _themeMode = ThemeMode.light;
    }
  }

  /// Save theme preference to SharedPreferences
  Future<void> _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeModeKey, _themeMode.index);
    } catch (e) {
      // Ignore errors when saving preferences
    }
  }
}
