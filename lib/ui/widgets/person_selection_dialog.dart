import 'package:astreal/models/birth_data.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 排序字段類型
enum SortFieldType {
  name,
  birthDate,
  birthPlace,
  createdAt,
}

/// 人物選擇對話框
class PersonSelectionDialog extends StatefulWidget {
  /// 人物列表
  final List<BirthData> personList;

  /// 當前選中的人物
  final BirthData? selectedPerson;

  /// 對話框標題
  final String title;

  /// 按鈕顏色
  final Color buttonColor;

  /// 是否顯示搜索框
  final bool showSearchBar;

  /// 是否顯示排序選項
  final bool showSortOptions;

  /// 構造函數
  const PersonSelectionDialog({
    Key? key,
    required this.personList,
    this.selectedPerson,
    this.title = '選擇人物',
    this.buttonColor = AppColors.royalIndigo,
    this.showSearchBar = true,
    this.showSortOptions = true,
  }) : super(key: key);

  @override
  State<PersonSelectionDialog> createState() => _PersonSelectionDialogState();
}

class _PersonSelectionDialogState extends State<PersonSelectionDialog> {
  /// 搜索關鍵字
  String _searchQuery = '';

  /// 排序字段
  SortFieldType _sortField = SortFieldType.name;

  /// 是否升序排序
  bool _isAscending = true;

  /// 過濾後的人物列表
  List<BirthData> _filteredList = [];

  /// 搜索控制器
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filteredList = List.from(widget.personList);
    _sortList(); // 先使用默認排序
    _loadSortPreferences(); // 然後異步加載用戶偏好設置
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加載排序偏好設置
  Future<void> _loadSortPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      final sortFieldIndex = prefs.getInt('personSelectionSortField') ?? SortFieldType.name.index;
      _sortField = SortFieldType.values[sortFieldIndex];
      _isAscending = prefs.getBool('personSelectionIsAscending') ?? true;
      _sortList();
    });
  }

  /// 保存排序偏好設置
  Future<void> _saveSortPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('personSelectionSortField', _sortField.index);
    await prefs.setBool('personSelectionIsAscending', _isAscending);
  }

  /// 排序列表
  void _sortList() {
    _filteredList.sort((a, b) {
      int result;
      switch (_sortField) {
        case SortFieldType.name:
          result = a.name.compareTo(b.name);
          break;
        case SortFieldType.birthDate:
          result = a.birthDate.compareTo(b.birthDate);
          break;
        case SortFieldType.birthPlace:
          result = a.birthPlace.compareTo(b.birthPlace);
          break;
        case SortFieldType.createdAt:
          result = a.createdAt.compareTo(b.createdAt);
          break;
      }
      return _isAscending ? result : -result;
    });
  }

  /// 過濾列表
  void _filterList() {
    if (_searchQuery.isEmpty) {
      _filteredList = List.from(widget.personList);
    } else {
      _filteredList = widget.personList.where((person) {
        return person.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            person.birthPlace.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            _formatDateTime(person.birthDate).contains(_searchQuery);
      }).toList();
    }
    _sortList();
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// 顯示排序選項對話框
  void _showSortOptionsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('排序方式'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 排序字段選項
                  ListTile(
                    title: const Text('姓名'),
                    leading: Radio<SortFieldType>(
                      value: SortFieldType.name,
                      groupValue: _sortField,
                      onChanged: (SortFieldType? value) {
                        if (value != null) {
                          setState(() {
                            _sortField = value;
                          });
                        }
                      },
                    ),
                  ),
                  ListTile(
                    title: const Text('出生日期'),
                    leading: Radio<SortFieldType>(
                      value: SortFieldType.birthDate,
                      groupValue: _sortField,
                      onChanged: (SortFieldType? value) {
                        if (value != null) {
                          setState(() {
                            _sortField = value;
                          });
                        }
                      },
                    ),
                  ),
                  ListTile(
                    title: const Text('出生地點'),
                    leading: Radio<SortFieldType>(
                      value: SortFieldType.birthPlace,
                      groupValue: _sortField,
                      onChanged: (SortFieldType? value) {
                        if (value != null) {
                          setState(() {
                            _sortField = value;
                          });
                        }
                      },
                    ),
                  ),
                  ListTile(
                    title: const Text('創建時間'),
                    leading: Radio<SortFieldType>(
                      value: SortFieldType.createdAt,
                      groupValue: _sortField,
                      onChanged: (SortFieldType? value) {
                        if (value != null) {
                          setState(() {
                            _sortField = value;
                          });
                        }
                      },
                    ),
                  ),
                  // 排序方向選項
                  SwitchListTile(
                    title: Text(_isAscending ? '升序排列' : '降序排列'),
                    value: _isAscending,
                    onChanged: (bool value) {
                      setState(() {
                        _isAscending = value;
                      });
                    },
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _sortList();
                  _saveSortPreferences();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
              ),
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 標題欄
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.buttonColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // 搜索欄
            if (widget.showSearchBar)
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索姓名、出生地點或日期',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                                _filterList();
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: widget.buttonColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                      _filterList();
                    });
                  },
                ),
              ),
            // 排序按鈕
            if (widget.showSortOptions)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      icon: const Icon(Icons.sort, size: 18),
                      label: Text(
                        '排序: ${_getSortFieldName(_sortField)} ${_isAscending ? '↑' : '↓'}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      onPressed: _showSortOptionsDialog,
                      style: TextButton.styleFrom(
                        foregroundColor: widget.buttonColor,
                      ),
                    ),
                  ],
                ),
              ),
            // 人物列表
            Flexible(
              child: _filteredList.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 48,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            '沒有符合條件的人物',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: _filteredList.length,
                      itemBuilder: (context, index) {
                        final person = _filteredList[index];
                        final isSelected = widget.selectedPerson?.id == person.id;
                        return Card(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: isSelected
                                ? BorderSide(color: widget.buttonColor, width: 2)
                                : BorderSide.none,
                          ),
                          elevation: isSelected ? 2 : 0,
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            leading: CircleAvatar(
                              backgroundColor: isSelected ? widget.buttonColor : Colors.grey.shade200,
                              child: Text(
                                person.name.isNotEmpty ? person.name.substring(0, 1) : '?',
                                style: TextStyle(
                                  color: isSelected ? Colors.white : Colors.grey.shade700,
                                ),
                              ),
                            ),
                            title: Text(
                              person.name,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4),
                                Text(
                                  _formatDateTime(person.birthDate),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  person.birthPlace,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                            selected: isSelected,
                            selectedTileColor: widget.buttonColor.withOpacity(0.05),
                            onTap: () {
                              Navigator.of(context).pop(person);
                            },
                          ),
                        );
                      },
                    ),
            ),
            // 底部按鈕
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('取消'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 獲取排序字段名稱
  String _getSortFieldName(SortFieldType sortField) {
    switch (sortField) {
      case SortFieldType.name:
        return '姓名';
      case SortFieldType.birthDate:
        return '出生日期';
      case SortFieldType.birthPlace:
        return '出生地點';
      case SortFieldType.createdAt:
        return '創建時間';
    }
  }
}
