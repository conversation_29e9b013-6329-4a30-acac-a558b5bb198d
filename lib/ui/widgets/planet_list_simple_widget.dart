import 'package:flutter/material.dart';

import '../../models/chart_data.dart';
import '../../models/planet_position.dart';
import '../../utils/astrology_calculator.dart' as astro;

/// 簡化版的行星列表小部件，直接接受 ChartData 參數
class SimplePlanetListWidget extends StatelessWidget {
  final ChartData chartData;

  const SimplePlanetListWidget({
    Key? key,
    required this.chartData,
  }) : super(key: key);

  /// 根據行星尊貴力量狀態返回對應的顏色
  Color _getDignityColor(PlanetDignity dignity) {
    switch (dignity) {
      case PlanetDignity.domicile:
        return Colors.purple; // 廟狀態用紫色
      case PlanetDignity.exaltation:
        return Colors.green; // 旺狀態用綠色
      case PlanetDignity.detriment:
        return Colors.red; // 陷狀態用紅色
      case PlanetDignity.fall:
        return Colors.orange; // 弱狀態用橙色
      case PlanetDignity.normal:
        return Colors.grey; // 普通狀態用灰色
      default:
        return Colors.grey;
    }
  }

  /// 格式化度數
  String _formatDegree(double longitude) {
    final int degree = longitude.floor();
    final int minute = ((longitude - longitude.floor()) * 60).round();
    return '$degree°$minute\'';
  }

  @override
  Widget build(BuildContext context) {
    final planets = chartData.planets;

    if (planets == null || planets.isEmpty) {
      return const Center(child: Text('無行星數據'));
    }

    return ListView.builder(
      itemCount: planets.length,
      itemBuilder: (context, index) {
        final planet = planets[index];
        final signDegree = planet.longitude % 30;
        final planetColor = astro.AstrologyCalculator.getPlanetColor(
          planet.name,
        );

        return Card(
          color: Colors.white,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: planetColor.withOpacity(0.3), width: 1.5),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 行星符號圓形圖標
                CircleAvatar(
                  backgroundColor: planetColor,
                  radius: 24,
                  child: Text(
                    planet.symbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontFamily: "astro_one_font",
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        planet.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.place,
                            size: 16,
                            color: Colors.black,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '位置: ${planet.sign} ${_formatDegree(signDegree)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.home_outlined,
                            size: 16,
                            color: Colors.black,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '宮位: ${planet.getHouseText()}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                      // 顯示行星尊貴力量狀態
                      Row(
                        children: [
                          const Icon(
                            Icons.auto_awesome,
                            size: 16,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '尊貴力量: ${planet.getDignityText()}',
                            style: TextStyle(
                              fontSize: 14,
                              color: _getDignityColor(planet.dignity),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                      // 顯示逆行狀態
                      if (planet.longitudeSpeed < 0)
                        Row(
                          children: [
                            const Icon(
                              Icons.replay,
                              size: 16,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              '逆行',
                              style: TextStyle(color: Colors.red, fontSize: 14),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
