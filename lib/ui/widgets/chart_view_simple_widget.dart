import 'package:flutter/material.dart';

import '../../models/chart_data.dart';
import '../../models/chart_type.dart';
import '../../widgets/chart_painter.dart';
import '../../widgets/dual_chart_painter.dart';

/// 簡化版的星盤視圖小部件，直接接受 ChartData 參數
class SimpleChartViewWidget extends StatefulWidget {
  final ChartData chartData;

  const SimpleChartViewWidget({
    Key? key,
    required this.chartData,
  }) : super(key: key);

  @override
  State<SimpleChartViewWidget> createState() => _SimpleChartViewWidgetState();
}

class _SimpleChartViewWidgetState extends State<SimpleChartViewWidget> {
  /// 根據圖表類型決定使用哪種繪製器
  CustomPainter _getChartPainter() {
    final chartType = widget.chartData.chartType;

    // 需要雙圈顯示的圖表類型
    if (_needsDualChartPainter()) {
      // 根據不同的圖表類型選擇適當的行星數據
      if (chartType == ChartType.synastry ||
          chartType == ChartType.synastrySecondary ||
          chartType == ChartType.synastryTertiary) {
        // 比較盤：本命盤和次要人物的行星
        return DualChartPainter(
          widget.chartData.primaryPerson.planets!, // 本命盤行星
          widget.chartData.secondaryPerson!.planets!, // 次要人物行星
          widget.chartData.aspects!,
          housesData: widget.chartData.houses!,
          chartType: chartType,
        );
      } else if (chartType == ChartType.transit) {
        // 行運盤：本命盤和當前行運行星
        return DualChartPainter(
          widget.chartData.primaryPerson.planets!, // 本命盤行星
          widget.chartData.planets!, // 行運盤行星
          widget.chartData.aspects!,
          housesData: widget.chartData.houses!,
          chartType: chartType,
        );
      } else {
        // 其他雙圈圖表：使用相同的行星數據
        return DualChartPainter(
          widget.chartData.planets!, // 本命盤行星
          widget.chartData.planets!, // 行運盤行星
          widget.chartData.aspects!,
          housesData: widget.chartData.houses!,
          chartType: chartType,
        );
      }
    } else {
      return ChartPainter(
        widget.chartData.planets!, // 行星數據
        widget.chartData.aspects!,
        housesData: widget.chartData.houses!,
        chartType: chartType,
      );
    }
  }

  /// 判斷是否需要使用雙圈星盤繪製器
  bool _needsDualChartPainter() {
    final chartType = widget.chartData.chartType;

    // 預測類星盤中的特定類型
    final progressionTypes = [
      ChartType.secondaryProgression,
      ChartType.tertiaryProgression,
      ChartType.solarArcDirection,
    ];

    // 返照盤類
    final returnTypes = [
      ChartType.solarReturn,
      ChartType.lunarReturn,
    ];

    // 需要雙圈顯示的特殊類型
    final specialDualTypes = [
      ChartType.transit,
      ChartType.synastry,
    ];

    return progressionTypes.contains(chartType) ||
        returnTypes.contains(chartType) ||
        specialDualTypes.contains(chartType) ||
        chartType.isSynastryProgression ||
        chartType.isCompositeProgression ||
        chartType.isDavisonProgression ||
        chartType.isMarksProgression;
  }

  /// 獲取圖表標題
  String _getChartTitle() {
    if (widget.chartData.chartType.requiresTwoPersons &&
        widget.chartData.secondaryPerson != null) {
      return '${widget.chartData.primaryPerson.name} 與 ${widget.chartData.secondaryPerson!.name} 的${widget.chartData.chartType.name}';
    } else {
      return '${widget.chartData.primaryPerson.name}的${widget.chartData.chartType.name}';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.chartData.planets == null || widget.chartData.houses == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final chartPainter = _getChartPainter();
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    const tabBarHeight = 48.0; // TabBar 的標準高度
    final topPadding = MediaQuery.of(context).padding.top;
    final availableHeight =
        screenHeight - appBarHeight - tabBarHeight - topPadding - 20;
    final chartSize = availableHeight * 0.6; // 使用可用高度的 60%

    // 使用統一的佈局結構
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 10),
          Text(
            _getChartTitle(),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),

          // 星盤圖
          SizedBox(
            height: chartSize,
            width: chartSize,
            child: CustomPaint(
              painter: _getChartPainter(),
            ),
          ),

          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
