import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:astreal/ui/widgets/person_selection_dialog.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../viewmodels/chart_viewmodel.dart';
import '../pages/chart_page.dart';

/// 人物選擇器元件，用於顯示當前選中的人物信息並提供選擇/更改人物的功能
class PersonSelectorWidget extends StatelessWidget {
  /// 當前選中的人物
  final BirthData? selectedPerson;

  /// 人物列表
  final List<BirthData> personList;

  /// 選擇人物時的回調函數
  final Function(BirthData) onPersonSelected;

  /// 編輯人物時的回調函數
  final Function(BirthData)? onEditPerson;

  /// 是否顯示編輯按鈕
  final bool showEditButton;

  /// 卡片標題
  final String title;

  /// 卡片圖標
  final IconData icon;

  /// 圖標顏色
  final Color iconColor;

  /// 對話框標題
  final String dialogTitle;

  /// 對話框按鈕顏色
  final Color dialogButtonColor;

  /// 是否正在加載數據
  final bool isLoading;

  /// 格式化日期時間的函數
  final String Function(DateTime) formatDateTime;

  /// 構造函數
  const PersonSelectorWidget({
    Key? key,
    required this.selectedPerson,
    required this.personList,
    required this.onPersonSelected,
    required this.formatDateTime,
    this.onEditPerson,
    this.showEditButton = true,
    this.title = '個人資訊',
    this.icon = Icons.person,
    this.iconColor = AppColors.solarAmber,
    this.dialogTitle = '選擇人物',
    this.dialogButtonColor = AppColors.royalIndigo,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (selectedPerson != null) {
            _navigateToNatalChart(context, selectedPerson!);
          } else {
            _showPersonSelectionDialog(context);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 標題和圖標
                  Row(
                    children: [
                      Icon(icon, color: iconColor, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                    ],
                  ),
                  // 操作按鈕
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (showEditButton &&
                          selectedPerson != null &&
                          onEditPerson != null)
                        IconButton(
                          icon: const Icon(Icons.edit, size: 20),
                          onPressed: () {
                            onEditPerson!(selectedPerson!);
                          },
                          tooltip: '編輯資料',
                          color: AppColors.royalIndigo,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          splashRadius: 20,
                        ),
                      const SizedBox(width: 16),
                      IconButton(
                        icon: const Icon(Icons.person_search, size: 20),
                        onPressed: () {
                          _showPersonSelectionDialog(context);
                        },
                        tooltip: '選擇人物',
                        color: AppColors.royalIndigo,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        splashRadius: 20,
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // 內容區域
              if (isLoading)
                const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                  ),
                )
              else if (selectedPerson == null)
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.person_add_alt_1,
                        size: 48,
                        color: AppColors.royalIndigo.withOpacity(0.3),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '點擊選擇人物以顯示個人資訊',
                        style: TextStyle(
                          color: AppColors.textMedium,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                )
              else
                _buildPersonInfo(selectedPerson!),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建人物信息區域
  Widget _buildPersonInfo(BirthData person) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow(Icons.person_outline, '姓名', person.name),
        const SizedBox(height: 8),
        _buildInfoRow(
            Icons.calendar_today, '出生時間', formatDateTime(person.birthDate)),
        const SizedBox(height: 8),
        _buildInfoRow(Icons.location_on, '出生地點', person.birthPlace),
        if (person.notes != null && person.notes!.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildInfoRow(Icons.note, '備註', person.notes!),
        ],
      ],
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: AppColors.textMedium),
        const SizedBox(width: 8),
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: const TextStyle(
              color: AppColors.textMedium,
              fontSize: 14,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
          ),
        ),
      ],
    );
  }

  /// 顯示人物選擇對話框
  Future<void> _showPersonSelectionDialog(BuildContext context) async {
    if (personList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
      );
      return;
    }

    final BirthData? result = await showDialog<BirthData>(
      context: context,
      builder: (BuildContext context) {
        return PersonSelectionDialog(
          personList: personList,
          selectedPerson: selectedPerson,
          title: dialogTitle,
          buttonColor: dialogButtonColor,
        );
      },
    );

    if (result != null) {
      onPersonSelected(result);
    }
  }

  /// 導航到本命盤頁面
  void _navigateToNatalChart(BuildContext context, BirthData birthData) {
    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(
            initialChartData: chartData,
            context: context,
          ),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }
}
