import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/services/astrology_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // 初始化 Flutter 綁定
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Davison and Marks Chart Tests', () {
    late AstrologyService astrologyService;
    late BirthData personA;
    late BirthData personB;
    late ChartData chartData;

    setUp(() {
      astrologyService = AstrologyService();

      // 創建兩個測試用的出生數據
      personA = BirthData(
        id: 'test_person_a',
        name: 'Test Person A',
        birthDate: DateTime(1990, 5, 15, 12, 0),
        birthPlace: 'Taipei',
        latitude: 25.0330,
        longitude: 121.5654,
      );

      personB = BirthData(
        id: 'test_person_b',
        name: 'Test Person B',
        birthDate: DateTime(1992, 8, 20, 18, 0),
        birthPlace: '<PERSON><PERSON><PERSON><PERSON>',
        latitude: 22.6273,
        longitude: 120.3014,
      );
    });

    test('Calculate Davison Chart', () async {
      // 創建時空中點盤數據
      chartData = ChartData(
        chartType: ChartType.davison,
        primaryPerson: personA,
        secondaryPerson: personB,
      );

      // 計算時空中點盤
      final result = await astrologyService.calculateChartData(chartData);

      // 驗證結果
      expect(result.chartType, equals(ChartType.davison));
      expect(result.planets, isNotNull);

      // 驗證時空中點盤的特性
      // 1. 時間應該是兩人出生時間的中點
      final midpointTimestamp = (personA.birthDate.millisecondsSinceEpoch +
                               personB.birthDate.millisecondsSinceEpoch) ~/ 2;
      final expectedMidpointDateTime = DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);

      // 2. 地點應該是兩人出生地點的中點
      final expectedMidpointLatitude = (personA.latitude + personB.latitude) / 2;
      final expectedMidpointLongitude = (personA.longitude + personB.longitude) / 2;

      // 由於我們無法直接訪問計算過程中的中點時間和地點，
      // 我們可以檢查行星是否已正確計算
      expect(result.planets!.length, greaterThan(0));
    });

    test('Calculate Marks Chart', () async {
      // 創建馬克思盤數據
      chartData = ChartData(
        chartType: ChartType.marks,
        primaryPerson: personA,
        secondaryPerson: personB,
      );

      // 計算馬克思盤
      final result = await astrologyService.calculateChartData(chartData);

      // 驗證結果
      expect(result.chartType, equals(ChartType.marks));
      expect(result.planets, isNotNull);

      // 驗證馬克思盤的特性
      // 1. 計算兩人出生時間的中點
      final midpointTimestamp = (personA.birthDate.millisecondsSinceEpoch +
                               personB.birthDate.millisecondsSinceEpoch) ~/ 2;
      final expectedMidpointDateTime = DateTime.fromMillisecondsSinceEpoch(midpointTimestamp);

      // 2. 計算第一個人的出生時間與中點的中點
      final expectedMarksTimestamp = (personA.birthDate.millisecondsSinceEpoch + midpointTimestamp) ~/ 2;
      final expectedMarksDateTime = DateTime.fromMillisecondsSinceEpoch(expectedMarksTimestamp);

      // 驗證馬克思盤的計算時間是否正確
      expect(result.specificDate, isNotNull);
      expect(result.specificDate!.millisecondsSinceEpoch, equals(expectedMarksDateTime.millisecondsSinceEpoch));

      // 驗證行星數據
      expect(result.planets!.length, greaterThan(0));
    });
  });
}
