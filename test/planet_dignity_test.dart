import 'package:astreal/models/planet_position.dart';
import 'package:astreal/services/astrology_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('行星尊貴力量測試', () {
    late AstrologyService astrologyService;

    setUp(() {
      astrologyService = AstrologyService();
    });

    test('太陽在獅子座應該是廟狀態', () async {
      // 創建一個太陽在獅子座的日期
      final DateTime birthDateTime = DateTime(2023, 8, 10); // 8月，太陽在獅子座
      
      // 計算行星位置
      final positions = await astrologyService.calculatePlanetPositions(
        birthDateTime,
        latitude: 25.0, // 台北緯度
        longitude: 121.5, // 台北經度
      );
      
      // 找到太陽
      final sun = positions.firstWhere((p) => p.name == '太陽');
      
      // 驗證太陽在獅子座
      expect(sun.sign, '獅子座');
      
      // 驗證太陽是廟狀態
      expect(sun.dignity, PlanetDignity.domicile);
    });

    test('月亮在金牛座應該是旺狀態', () async {
      // 創建一個月亮在金牛座的日期
      final DateTime birthDateTime = DateTime(2023, 5, 5); // 5月，月亮可能在金牛座
      
      // 計算行星位置
      final positions = await astrologyService.calculatePlanetPositions(
        birthDateTime,
        latitude: 25.0,
        longitude: 121.5,
      );
      
      // 找到月亮
      final moon = positions.firstWhere((p) => p.name == '月亮');
      
      // 輸出月亮的星座和尊貴力量狀態，用於調試
      print('月亮在 ${moon.sign}，尊貴力量狀態: ${moon.getDignityText()}');
      
      // 如果月亮在金牛座，驗證它是旺狀態
      if (moon.sign == '金牛座') {
        expect(moon.dignity, PlanetDignity.exaltation);
      }
    });

    test('水星在雙魚座應該是弱狀態', () async {
      // 創建一個水星在雙魚座的日期
      final DateTime birthDateTime = DateTime(2023, 3, 10); // 3月，水星可能在雙魚座
      
      // 計算行星位置
      final positions = await astrologyService.calculatePlanetPositions(
        birthDateTime,
        latitude: 25.0,
        longitude: 121.5,
      );
      
      // 找到水星
      final mercury = positions.firstWhere((p) => p.name == '水星');
      
      // 輸出水星的星座和尊貴力量狀態，用於調試
      print('水星在 ${mercury.sign}，尊貴力量狀態: ${mercury.getDignityText()}');
      
      // 如果水星在雙魚座，驗證它是弱狀態
      if (mercury.sign == '雙魚座') {
        expect(mercury.dignity, PlanetDignity.fall);
      }
    });
  });
}
