import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/planet_position.dart';
import 'package:astreal/services/astrology_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('相位方向（入相/出相）測試', () {
    late AstrologyService astrologyService;

    setUp(() {
      astrologyService = AstrologyService();
    });

    test('行星接近相位時應該是入相', () {
      // 創建兩個行星位置
      final planet1 = PlanetPosition(
        id: 0,
        name: '太陽',
        symbol: '☉',
        longitude: 0.0, // 0度
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 1.0, // 正向移動
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '牡羊座',
        house: 1,
      );

      final planet2 = PlanetPosition(
        id: 1,
        name: '月亮',
        symbol: '☽',
        longitude: 88.0, // 88度，接近90度四分相
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.5, // 正向移動，但比太陽慢
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '巨蟹座',
        house: 4,
      );

      // 測試四分相（90度）
      final direction = astrologyService.getAspectDirection(planet2, planet1, 90.0);

      // 計算角度差和相對速度（用於調試）
      double angleDiff = (planet2.longitude - planet1.longitude) % 360;
      if (angleDiff > 180) angleDiff = 360 - angleDiff;
      double relativeSpeed = planet2.longitudeSpeed - planet1.longitudeSpeed;

      print('測試1: 角度差=$angleDiff, 相位角度=90.0, 相對速度=$relativeSpeed');
      print('測試1: 結果=${direction == AspectDirection.applying ? "入相" : "出相"}');

      // 由於行星正在接近90度相位，應該是入相
      expect(direction, AspectDirection.applying);
    });

    test('行星遠離相位時應該是出相', () {
      // 創建兩個行星位置
      final planet1 = PlanetPosition(
        id: 0,
        name: '太陽',
        symbol: '☉',
        longitude: 0.0, // 0度
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 1.0, // 正向移動
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '牡羊座',
        house: 1,
      );

      final planet2 = PlanetPosition(
        id: 1,
        name: '月亮',
        symbol: '☽',
        longitude: 92.0, // 92度，剛剛超過90度四分相
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 0.5, // 正向移動，但比太陽慢
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '巨蟹座',
        house: 4,
      );

      // 測試四分相（90度）
      final direction = astrologyService.getAspectDirection(planet2, planet1, 90.0);

      // 計算角度差和相對速度（用於調試）
      double angleDiff = (planet2.longitude - planet1.longitude) % 360;
      if (angleDiff > 180) angleDiff = 360 - angleDiff;
      double relativeSpeed = planet2.longitudeSpeed - planet1.longitudeSpeed;

      print('測試2: 角度差=$angleDiff, 相位角度=90.0, 相對速度=$relativeSpeed');
      print('測試2: 結果=${direction == AspectDirection.applying ? "入相" : "出相"}');

      // 由於行星正在遠離90度相位，應該是出相
      expect(direction, AspectDirection.separating);
    });

    test('行星逆行時接近相位應該是入相', () {
      // 創建兩個行星位置
      final planet1 = PlanetPosition(
        id: 0,
        name: '太陽',
        symbol: '☉',
        longitude: 0.0, // 0度
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 1.0, // 正向移動
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '牡羊座',
        house: 1,
      );

      final planet2 = PlanetPosition(
        id: 5,
        name: '土星',
        symbol: '♄',
        longitude: 92.0, // 92度，剛剛超過90度四分相
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: -0.5, // 逆行
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '巨蟹座',
        house: 4,
      );

      // 測試四分相（90度）
      final direction = astrologyService.getAspectDirection(planet1, planet2, 90.0);

      // 計算角度差和相對速度（用於調試）
      double angleDiff = (planet2.longitude - planet1.longitude) % 360;
      if (angleDiff > 180) angleDiff = 360 - angleDiff;
      double relativeSpeed = planet2.longitudeSpeed - planet1.longitudeSpeed;

      print('測試3: 角度差=$angleDiff, 相位角度=90.0, 相對速度=$relativeSpeed');
      print('測試3: 結果=${direction == AspectDirection.applying ? "入相" : "出相"}');

      // 由於土星逆行，正在接近90度相位，應該是入相
      expect(direction, AspectDirection.applying);
    });

    test('行星逆行時遠離相位應該是出相', () {
      // 創建兩個行星位置
      final planet1 = PlanetPosition(
        id: 0,
        name: '太陽',
        symbol: '☉',
        longitude: 0.0, // 0度
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: 1.0, // 正向移動
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '牡羊座',
        house: 1,
      );

      final planet2 = PlanetPosition(
        id: 5,
        name: '土星',
        symbol: '♄',
        longitude: 88.0, // 88度，接近90度四分相
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: -0.5, // 逆行
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '巨蟹座',
        house: 4,
      );

      // 測試四分相（90度）
      final direction = astrologyService.getAspectDirection(planet1, planet2, 90.0);

      // 計算角度差和相對速度（用於調試）
      double angleDiff = (planet2.longitude - planet1.longitude) % 360;
      if (angleDiff > 180) angleDiff = 360 - angleDiff;
      double relativeSpeed = planet2.longitudeSpeed - planet1.longitudeSpeed;

      print('測試4: 角度差=$angleDiff, 相位角度=90.0, 相對速度=$relativeSpeed');
      print('測試4: 結果=${direction == AspectDirection.applying ? "入相" : "出相"}');

      // 由於土星逆行，正在遠離90度相位，應該是出相
      expect(direction, AspectDirection.separating);
    });

    test('兩個行星都逆行時的入相判斷', () {
      // 創建兩個行星位置
      final planet1 = PlanetPosition(
        id: 4,
        name: '木星',
        symbol: '♃',
        longitude: 0.0, // 0度
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: -0.3, // 逆行
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '牡羊座',
        house: 1,
      );

      final planet2 = PlanetPosition(
        id: 5,
        name: '土星',
        symbol: '♄',
        longitude: 118.0, // 118度，接近120度三分相
        latitude: 0.0,
        distance: 0.0,
        longitudeSpeed: -0.5, // 逆行，比木星快
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: '獅子座',
        house: 5,
      );

      // 測試三分相（120度）
      final direction = astrologyService.getAspectDirection(planet2, planet1, 120.0);

      // 計算角度差和相對速度（用於調試）
      double angleDiff = (planet2.longitude - planet1.longitude) % 360;
      if (angleDiff > 180) angleDiff = 360 - angleDiff;
      double relativeSpeed = planet2.longitudeSpeed - planet1.longitudeSpeed;

      print('測試5: 角度差=$angleDiff, 相位角度=120.0, 相對速度=$relativeSpeed');
      print('測試5: 結果=${direction == AspectDirection.applying ? "入相" : "出相"}');

      // 由於土星逆行速度快，正在接近120度相位，應該是入相
      expect(direction, AspectDirection.applying);
    });
  });
}
