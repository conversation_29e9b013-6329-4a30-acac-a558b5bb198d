name: astreal
description: "A professional astrology app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  shared_preferences: ^2.5.3
  sweph: ^3.2.1+2.10.3
  path_provider: ^2.1.1
  universal_ffi: ^1.1.3
  turf: ^0.0.10
  logger: ^2.5.0
  timezone: ^0.10.0
  flutter_email_sender: ^6.0.2
  pdf: ^3.11.3
  printing: ^5.14.2
  share_plus: ^7.2.1
  flutter_pdfview: ^1.3.2
  geocoding: ^2.1.1
  http: ^1.2.0
  firebase_core: ^3.13.0
  firebase_database: ^11.3.5
  cloud_firestore: ^5.6.7
  mailer: ^6.0.1
  flutter_markdown: ^0.6.18
  url_launcher: ^6.2.4
  geolocator: ^14.0.0
  clipboard: ^0.1.3
  flutter_native_splash: ^2.3.5
  flutter_launcher_icons: ^0.13.1
  provider: ^6.1.2
  file_picker: ^10.1.2
  csv: ^6.0.0
  intl: ^0.18.1
  uuid: ^4.5.1
  flutter_datetime_picker_plus: ^2.1.0
  package_info_plus: ^4.2.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An images asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/json/planet.json
    - assets/json/sign.json
    - assets/ephe/
    - assets/ephe/seas_18.se1
    - assets/ephe/sefstars.txt
    - assets/ephe/seasnam.txt
    - assets/ephe/se00433s.se1
    - assets/ephe/sepl_18.se1
    - assets/ephe/semo_18.se1
    - assets/combined.json
    - assets/images/flutter_launcher_icons.png

  fonts:
    - family: astro_one_font
      fonts:
        - asset: assets/fonts/astro_one_font.ttf

flutter_native_splash:
  color: "#3F51B5"
  image: assets/images/flutter_launcher_icons.png
  android_gravity: center
  ios_content_mode: center
  fullscreen: true
  android: true
  ios: true
  web: true

  android_12:
    image: assets/images/flutter_launcher_icons.png
    icon_background_color: "#3F51B5"
    image_dark: assets/images/flutter_launcher_icons.png
    icon_background_color_dark: "#3F51B5"


flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/flutter_launcher_icons.png" # 你準備好的圖示
  adaptive_icon_background: "#3F51B5"      # Android adaptive icon 背景色（可自訂）
  adaptive_icon_foreground: "assets/images/flutter_launcher_icons.png" # 可選