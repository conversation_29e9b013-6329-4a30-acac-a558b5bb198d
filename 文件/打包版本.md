當你用 Flutter 開發完成 App 之後，就會進入「打包（build）」階段，這意味著你要把 App 編譯成可在 Android、iOS、Web 或桌面系統上運行的格式。以下是 Flutter 的主要打包方式整理，幫你一目了然地掌握不同平台的流程與常見指令。

---

## 🌟 通用建議

* 確保你已經執行過：

  ```bash
  flutter clean
  flutter pub get
  ```

* 指定打包模式：

   * `--debug`（開發階段）
   * `--profile`（效能分析用）
   * `--release`（正式發布）

---

## 📱 Android 打包

### ✅ APK

```bash
flutter build apk --debug
flutter build apk --release
```

產出路徑：

```
build/app/outputs/flutter-apk/app-release.apk
```

#### 更多選項：

```bash
flutter build apk --split-per-abi
```

可生成多個 APK，對應不同 CPU 架構（如 arm64-v8a、armeabi-v7a）。

---

### ✅ AAB（Google Play 推薦格式）

```bash
flutter build appbundle --release
```

產出路徑：

```
build/app/outputs/bundle/release/app-release.aab
```

---

## 🍎 iOS 打包

### ✅ 建構 release build（需 macOS + Xcode）

```bash
flutter build ios --release
```

產出後：

* 需用 Xcode 開啟 `ios/Runner.xcworkspace` 來做簽章與打包成 `.ipa`
* 或使用 `xcodebuild` 進一步操作

---

## 🌐 Web 打包

```bash
flutter build web
```

產出路徑：

```
build/web/
```

適用部署於 Firebase Hosting、Netlify、GitHub Pages 等靜態網站平台。

---

## 🖥️ 桌面平台（macOS / Windows / Linux）

### macOS：

```bash
flutter build macos
```

產出在：

```
build/macos/Build/Products/Release/
```

### Windows：

```bash
flutter build windows
```

### Linux：

```bash
flutter build linux
```

---

## 📦 常見補充工具與技巧

### 壓縮資源體積

```bash
flutter build apk --release --obfuscate --split-debug-info=build/symbols/
```

* `--obfuscate`: 混淆 Dart 原始碼（提升安全）
* `--split-debug-info`: 儲存符號表，有助日後錯誤追蹤

---

## 🧪 檢查建構環境

```bash
flutter doctor
```

---

## 🛠 自動化（CI/CD）

* 可透過 GitHub Actions、Bitrise、Codemagic 等自動化流程打包與部署。
* 若部署至 Play Store、App Store，請準備好金鑰與憑證、設定 keystore（Android）與 provisioning profile（iOS）。

---

需要我幫你整理一份打包腳本，或者幫你建立打包自動化流程的初始設定嗎？ 😎

---

## 🎯 各平台「可安裝／執行」的 Flutter 打包方式

flutter build apk --debug

---

### 🪟 Windows：打包 exe 安裝資料夾

```bash
flutter build windows
```

📂 產出路徑：

```
build/windows/runner/Release/
```

內容包括：

* `.exe` 執行檔（你的 App）
* 所需 DLL 等資源

🛠️ 如果想打包成安裝器（installer），可以用：

* [Inno Setup](https://jrsoftware.org/isinfo.php)
* [NSIS](https://nsis.sourceforge.io/)

這些工具可以把整個資料夾打包成 `.exe 安裝程式`，讓使用者像安裝一般 Windows 程式一樣方便。

---

### 🍎 macOS：打包 .app 或 dmg

```bash
flutter build macos
```

📂 產出路徑：

```
build/macos/Build/Products/Release/YourApp.app
```

這是一個原生的 `.app`，你可以直接打開或分享。

📦 若要包成 `.dmg` 安裝檔可用工具如：

* [`create-dmg`](https://github.com/create-dmg/create-dmg)
* `hdiutil`（macOS 內建）

```bash
hdiutil create -volname YourApp -srcfolder path/to/YourApp.app -ov -format UDZO YourApp.dmg
```

---

### 🐧 Linux：打包可執行檔

```bash
flutter build linux
```

📂 產出路徑：

```
build/linux/x64/release/bundle/
```

裡面有：

* 一個可執行檔（你的 App）
* 其他需要的資源與執行依賴

可以壓縮打包成 `.tar.gz` 或 `.deb` 安裝包（進階可用 `dpkg-deb` 建立 .deb）。

---

### 🌐 Web：打包網頁站台（無需安裝）

```bash
flutter build web
```

📂 產出路徑：

```
build/web/
```

你可以把它部署到：

* GitHub Pages
* Firebase Hosting
* Netlify / Vercel
* 自己的伺服器

---

## 🧠 快速整理（表格版）

| 平台      | 指令                      | 可執行產物             | 是否需外部工具打 installer   |
| ------- | ----------------------- | ----------------- | -------------------- |
| Android | `flutter build apk`     | `app-release.apk` | 否                    |
| iOS     | `flutter build ios`     | `.ipa`（透過 Xcode）  | 是（TestFlight 或匯出）    |
| Windows | `flutter build windows` | `.exe + 資源資料夾`    | ✅ 建議用 Inno Setup     |
| macOS   | `flutter build macos`   | `.app`（或 `.dmg`）  | ✅ 可用 `hdiutil` 打 dmg |
| Linux   | `flutter build linux`   | 執行檔 + bundle      | ✅ 可自行包成 `.deb` 等格式   |
| Web     | `flutter build web`     | HTML/CSS/JS 網頁    | 否（直接部署）              |

---

想幫你更進一步設計自動化流程時，我想問問：
你現在測試的平台有哪些？要不要我幫你打包一個跨平台的簡易 installer 腳本？🤹‍♂️



macOS
我已經為您準備好了將應用程序打包成 .dmg 安裝檔的所有必要文件和腳本。以下是我所做的更改和準備工作：
1. 更新了 macOS 應用程序的配置文件
   在 Release.entitlements 和 DebugProfile.entitlements 中添加了文件讀寫權限：
   com.apple.security.files.user-selected.read-write
   com.apple.security.files.downloads.read-write
   更新了 Info.plist 文件：
   設置應用程序名稱為 "Astreal"
   添加了應用程序類別 (Lifestyle)
   添加了版權信息
2. 創建了自動化打包腳本 create_dmg.sh
   這個腳本會自動執行以下步驟：
   清理之前的構建
   獲取依賴
   構建 macOS 應用程序
   創建 .dmg 安裝檔，包含應用程序和指向 Applications 文件夾的快捷方式
   打開包含 DMG 文件的目錄
   如何使用
   要創建 .dmg 安裝檔，只需在終端中執行以下命令：
   腳本執行完成後，會在項目根目錄生成 Astreal-1.0.0.dmg 文件，這就是您的安裝檔。
   注意事項
   如果您想更改版本號，可以編輯 create_dmg.sh 文件中的 VERSION 變量。
   如果您的應用程序名稱不是 "Astreal"，請同時更新 Info.plist 中的 CFBundleName 和腳本中的 APP_NAME 變量。
   在分發應用程序之前，建議您添加一個適當的應用程序圖標。
   現在您可以運行腳本來生成 .dmg 安裝檔了。這個安裝檔可以分發給 macOS 用戶，他們只需雙擊 .dmg 文件，然後將應用程序拖到 Applications 文件夾即可完成安裝。